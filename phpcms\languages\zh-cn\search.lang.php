<?php
$LANG['add_search_type'] = '添加搜索分类';

$LANG['select_model_name'] = '所属模型';
$LANG['select_module_name'] = '所属模块';
$LANG['type_name'] = '类型名称';
$LANG['description'] = '描述';

$LANG['re_index_note'] = '1、重建索引将会清空原有的所有的索引内容 , 每轮更新';
$LANG['tiao'] = '条';
$LANG['confirm_reindex'] = '确认重建索引';
$LANG['basic_setting'] = '基本配置';
$LANG['sphinx_setting'] = 'sphinx全文索引配置';
$LANG['enable_search'] = '是否启用全站搜索';
$LANG['relationenble'] = '是否启用相关搜索';
$LANG['relationenble_note'] = '（此项功能会增大数据库压力）';
$LANG['suggestenable'] = '是否启用建议搜索';
$LANG['suggestenable_note'] = '（提示数据来源google）';
$LANG['sphinxenable'] = '是否启用sphinx全文索引';
$LANG['host'] = '服务器主机地址';
$LANG['port'] = '服务器端口号';
$LANG['testing'] = '测试中...';
$LANG['testsuccess'] = '链接成功';
$LANG['hostempty'] = '主机名为空';
$LANG['portempty'] = '端口为空';
$LANG['test'] = '测试';
$LANG['sort'] = '排序';
$LANG['catname'] = '类别名称';
$LANG['modulename'] = '所属模块';
$LANG['modlename'] = '所属模型';
$LANG['catdescription'] = '类别描述';
$LANG['opreration'] = '管理操作';
$LANG['modify'] = '修改';
$LANG['content_module'] = '内容模块';
$LANG['edit_cat'] = '编辑分类';
$LANG['searh_notice'] = '注意：全文检索模块需要mysql开启全文索引功能，<br />开启方法：修改mysql配置文件：window服务器为my.ini，linux服务器为my.cnf，在 [mysqld] 后面加入一行“ft_min_word_len=1”，然后重启Mysql。

';
?>