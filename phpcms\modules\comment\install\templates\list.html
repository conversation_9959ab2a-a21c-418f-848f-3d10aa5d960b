{template "content","header"}
{pc:comment action="get_comment" commentid="$commentid"}
{php $comment = $data;}
{/pc}
<div class="main">
        <h2 class="comment-title blue"><a href="{if $comment[url]}{$comment[url]}{else}{$url}{/if}">{if $comment[title]}{$comment[title]}{else}{$title}{/if}</a> <a href="#comment" class="f12 fn"><font color="#FF0000">[我来说两句]</font></a></h2>
        <div class="comment_button"><a href="{APP_PATH}index.php?m=comment&c=index&a=init&commentid={$commentid}&title={urlencode(($comment[title] ? $comment[title] : $title))}&url={urlencode(($comment[url] ? $comment[url] : $url))}&hot=0"{if empty($hot)} class="on"{/if}>最新</a> <a href="{APP_PATH}index.php?m=comment&c=index&a=init&commentid={$commentid}&title={urlencode(($comment[title] ? $comment[title] : $title))}&url={urlencode(($comment[url] ? $comment[url] : $url))}&hot=1"{if $hot} class="on"{/if}>最热</a></div> 	
	<div class="col-left">
       <div class="comment">
       <h4 class="f14">评论列表<span class="f12 fn">（评论 <font color="red">{if $comment[total]}{$comment[total]}{else}0{/if}</font>）以下网友评论只代表网友个人观点，不代表本站观点。</span></h4>
{pc:comment action="lists" commentid="$commentid" siteid="$siteid" page="$_GET[page]" hot="$hot" num="20"}
{loop $data $r}
    <h5 class="title fn">{direction($r[direction])} <font color="#FF0000">{format::date($r[creat_at], 1)}</font> {if $r[userid]}{get_nickname($r[userid])}{else}{$r[username]}{/if} </h5>
    <div class="content">{stripcslashes($r[content])}
	<div class="rt"><a href="javascript:void(0)" onclick="reply({$r[id]}, '{$commentid}')">回复</a>  <a href="javascript:void(0)" onclick="support({$r[id]}, '{$commentid}')">支持</a>（<font id="support_{$r[id]}">{$r[support]}</font>）
	</div>
	<div id="reply_{$r[id]}" style="display:none"></div>
	</div>
	
  <div class="bk30 hr mb8"></div>
  {/loop}
 
 
</div>
 <div id="pages" class="text-r">{$pages}</div>
 {/pc}
<div class="bk10"></div><div class="comment-form">
<form action="{APP_PATH}index.php?m=comment&c=index&a=post&commentid={$commentid}" method="post">
<input type="hidden" name="title" value="{urlencode(($comment[title] ? $comment[title] : $title))}">
<input type="hidden" name="url" value="{urlencode(($comment[url] ? $comment[url] : $url))}">
      <a name="comment"></a>
      	<h5><strong>我来说两句</strong></h5>
        <div class="posn">我的态度：<input type="radio" name="direction" value="1" /> <img src="{IMG_PATH}icon/zheng.png" /> <input type="radio" name="direction" value="2" /> <img src="{IMG_PATH}icon/fan.png" /> <input type="radio" name="direction" value="3"  /> <img src="{IMG_PATH}icon/zhong.png" />
</div>
        <textarea rows="8" cols="80" name="content"></textarea><br>
		{if $setting[code]}
		
		  <label>验证码：<input type="text" name="code"  class="input-text" id="yzmText" onfocus="var offset = $(this).offset();$('#yzm').css({'left': +offset.left-8, 'top': +offset.top-$('#yzm').height()});$('#yzm').show();$('#yzmText').data('hide', 1)" onblur='$("#yzmText").data("hide", 0);setTimeout("hide_code()", 3000)' /></label>
		  <div id="yzm" class="yzm">{form::checkcode()}<br />点击图片更换</a></div>
        <div class="bk10"></div>
		{/if}
        <div class="btn"><input type="submit" value="发表评论" /></div>&nbsp;&nbsp;&nbsp;&nbsp;{if $userid}{get_nickname()} <a href="{APP_PATH}index.php?m=member&c=index&a=logout&forward={urlencode(get_url())}">退出</a>{else}<a href="{APP_PATH}index.php?m=member&c=index&a=login&forward={urlencode(get_url())}" class="blue">登录</a><span> | </span><a href="{APP_PATH}index.php?m=member&c=index&a=register" class="blue">注册</a>  {if !$setting[guest]}<span style="color:red">需要登陆才可发布评论</span>{/if}{/if}
	</form>
<style type="text/css">
.look-content{ padding:10px;border:1px dashed #ffbf7a; background:#fffced; margin:10px auto}
.look-content ul{ color:#666}
</style>
            <div class="look-content">
            <h6>请您注意：</h6>
                <ul>
                    <li>自觉遵守：爱国、守法、自律、真实、文明的原则</li>
                    <li>尊重网上道德，遵守《全国人大常委会关于维护互联网安全的决定》及中华人民共和国其他各项有关法律法规</li>
                    <li>严禁发表危害国家安全，破坏民族团结、国家宗教政策和社会稳定，含侮辱、诽谤、教唆、淫秽等内容的作品</li>
                    <li>承担一切因您的行为而直接或间接导致的民事或刑事法律责任</li>
                    <li>您在本站评论发表的作品，本站有权在网站内保留、转载、引用或者删除</li>
                    <li>参与本评论即表明您已经阅读并接受上述条款</li>
                </ul>
            </div>
      </div>
  </div>
    <div class="col-auto">
        <div class="box">
            <h5 class="title-2">评论排行</h5>
            {pc:comment action="bang" cache="3600"}
            <ul class="content list f12 lh22">
				{loop $data $r}
                <li>·<a href="{$r[url]}" title="{$r[title]}">{str_cut($r[title], 26)}</a>({$r[total]})</li>
				{/loop}
            </ul>
            {/pc}
        </div>
        <div class="bk10"></div>
    </div>
</div>
<script type="text/javascript">
function support(id, commentid) {
	$.getJSON('{APP_PATH}index.php?m=comment&c=index&a=support&format=jsonp&commentid='+commentid+'&id='+id+'&callback=?', function(data){
		if(data.status == 1) {
			$('#support_'+id).html(parseInt($('#support_'+id).html())+1);
		} else {
			alert(data.msg);
		}
	});
}

function reply(id,commentid) {
	var str = '<form action="{APP_PATH}index.php?m=comment&c=index&a=post&commentid='+commentid+'&id='+id+'" method="post"><textarea rows="10" style="width:100%" name="content"></textarea>{if $setting[code]}<label>验证码：<input type="text" name="code"  class="input-text" onfocus="var offset = $(this).offset();$(\'#yzm\').css({\'left\': +offset.left-8, \'top\': +offset.top-$(\'#yzm\').height()});$(\'#yzm\').show();$(\'#yzmText\').data(\'hide\', 1)" onblur=\'$("#yzmText").data("hide", 0);setTimeout("hide_code()", 3000)\' /></label>{/if}  <div class="btn"><input type="submit" value="发表评论" /></div>&nbsp;&nbsp;&nbsp;&nbsp;{if $userid}{get_nickname()} <a href="{APP_PATH}index.php?m=member&c=index&a=logout&forward={urlencode(get_url())}">退出</a>{else}<a href="{APP_PATH}index.php?m=member&c=index&a=login&forward={urlencode(get_url())}" class="blue">登录</a> | <a href="{APP_PATH}index.php?m=member&c=index&a=register" class="blue">注册</a>  {if !$setting[guest]}<span style="color:red">需要登陆才可发布评论</span>{/if}{/if}</form>';
	$('#reply_'+id).html(str).toggle();
}

function hide_code() {
	if ($('#yzmText').data('hide')==0) {
		$('#yzm').hide();
	}
}
</script>
{template "content","footer"}