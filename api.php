<?php 
/**
 *  index.php API 入口
 *
 * @copyright			(C) 2005-2010 PHPCMS
 * @license				http://www.phpcms.cn/license/
 * @lastmodify			2010-7-26
 */
define('PHPCMS_PATH', dirname(__FILE__).DIRECTORY_SEPARATOR);
include PHPCMS_PATH.'phpcms/base.php';
$param = pc_base::load_sys_class('param');
$_userid = param::get_cookie('_userid');
if($_userid) {
	$member_db = pc_base::load_model('member_model');
	$_userid = intval($_userid);
	$memberinfo = $member_db->get_one(array('userid'=>$_userid),'islock');
	if($memberinfo['islock']) exit('<h1>Bad Request!</h1>');
}
$op = isset($_GET['op']) && trim($_GET['op']) ? trim($_GET['op']) : exit('Operation can not be empty');
if (isset($_GET['callback']) && !preg_match('/^[a-zA-Z_][a-zA-Z0-9_]+$/', $_GET['callback']))  unset($_GET['callback']);

// 安全修复：加强文件包含验证
// 1. 严格限制op参数只能包含字母、数字和下划线
if (!preg_match('/^[a-zA-Z0-9_]+$/', $op)) {
	exit('Invalid operation parameter');
}

// 2. 白名单验证，只允许特定的API文件
$allowed_apis = array(
	'add_favorite', 'checkcode', 'count', 'creatimg', 'get_keywords',
	'get_linkage', 'get_menu', 'map', 'phpsso', 'reg_send_sms',
	'sms', 'sms_idcheck', 'video_api'
);

if (!in_array($op, $allowed_apis)) {
	exit('API handler not allowed');
}

// 3. 验证文件存在且在正确目录
$api_file = PHPCMS_PATH.'api/'.$op.'.php';
$real_path = realpath($api_file);
$api_dir = realpath(PHPCMS_PATH.'api/');

if (!$real_path || !$api_dir || strpos($real_path, $api_dir) !== 0) {
	exit('API handler does not exist');
}

if (file_exists($api_file)) {
	include $api_file;
} else {
	exit('API handler does not exist');
}
?>