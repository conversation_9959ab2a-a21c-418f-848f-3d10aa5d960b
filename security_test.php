<?php
/**
 * PHPCMS 安全修复测试文件
 * 用于验证漏洞修复效果
 * 
 * 访问方式: http://localhost/security_test.php
 * 测试完成后请删除此文件
 */

// 包含PHPCMS基础文件
define('PHPCMS_PATH', dirname(__FILE__).DIRECTORY_SEPARATOR);
include PHPCMS_PATH.'phpcms/base.php';

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>PHPCMS 安全修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-item { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .danger { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        h1 { color: #333; }
        h2 { color: #666; margin-top: 30px; }
        code { background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
        .test-code { background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🛡️ PHPCMS 安全修复测试报告</h1>
    <p><strong>测试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    <p><strong>PHP版本:</strong> <?php echo PHP_VERSION; ?></p>
    
    <h2>1. 代码执行漏洞修复测试</h2>
    <?php
    echo '<div class="test-item">';
    echo '<h3>测试 string2array 函数</h3>';
    
    // 测试原来的危险输入
    $dangerous_input = 'array(phpinfo())';
    $result = string2array($dangerous_input);
    
    if (empty($result)) {
        echo '<div class="success">✅ <strong>修复成功:</strong> 危险的PHP数组格式已被阻止</div>';
    } else {
        echo '<div class="danger">❌ <strong>修复失败:</strong> 仍然存在代码执行风险</div>';
    }
    
    // 测试正常的JSON输入
    $json_input = '{"name":"test","value":123}';
    $json_result = string2array($json_input);
    
    if (is_array($json_result) && isset($json_result['name'])) {
        echo '<div class="success">✅ <strong>功能正常:</strong> JSON格式解析正常工作</div>';
    } else {
        echo '<div class="warning">⚠️ <strong>注意:</strong> JSON解析可能受到影响</div>';
    }
    
    echo '<div class="test-code"><strong>测试代码:</strong><br>';
    echo 'string2array(\'array(phpinfo())\') // 应该返回空数组<br>';
    echo 'string2array(\'{"name":"test","value":123}\') // 应该正常解析';
    echo '</div>';
    echo '</div>';
    ?>
    
    <h2>2. XSS过滤函数测试</h2>
    <?php
    echo '<div class="test-item">';
    echo '<h3>测试 safe_replace 和 remove_xss 函数</h3>';
    
    $xss_tests = array(
        '<script>alert("xss")</script>',
        'javascript:alert(1)',
        'onmouseover="alert(1)"',
        '<?php phpinfo(); ?>',
        'eval(alert(1))'
    );
    
    $all_safe = true;
    foreach ($xss_tests as $test) {
        $filtered = safe_replace($test);
        $xss_filtered = remove_xss($test);
        
        if (strpos($filtered, '<script') !== false || strpos($filtered, 'javascript:') !== false || 
            strpos($filtered, 'eval') !== false || strpos($xss_filtered, '<script') !== false) {
            $all_safe = false;
            echo '<div class="danger">❌ 危险输入未完全过滤: ' . htmlspecialchars($test) . '</div>';
        }
    }
    
    if ($all_safe) {
        echo '<div class="success">✅ <strong>修复成功:</strong> XSS过滤函数工作正常</div>';
    }
    
    echo '<div class="test-code"><strong>测试的危险输入:</strong><br>';
    foreach ($xss_tests as $test) {
        echo htmlspecialchars($test) . '<br>';
    }
    echo '</div>';
    echo '</div>';
    ?>
    
    <h2>3. 文件上传安全测试</h2>
    <?php
    echo '<div class="test-item">';
    echo '<h3>文件上传安全检查</h3>';
    
    // 检查上传目录的.htaccess文件
    $upload_htaccess = PHPCMS_PATH . 'uploadfile/.htaccess';
    if (file_exists($upload_htaccess)) {
        echo '<div class="success">✅ <strong>安全配置:</strong> 上传目录已配置.htaccess保护</div>';
    } else {
        echo '<div class="warning">⚠️ <strong>建议:</strong> 上传目录缺少.htaccess保护文件</div>';
    }
    
    // 检查attachment类是否存在新的安全方法
    if (method_exists('attachment', 'check_file_content')) {
        echo '<div class="success">✅ <strong>修复成功:</strong> 文件内容验证功能已添加</div>';
    } else {
        echo '<div class="warning">⚠️ <strong>注意:</strong> 文件内容验证方法可能未正确添加</div>';
    }
    
    echo '</div>';
    ?>
    
    <h2>4. API安全测试</h2>
    <?php
    echo '<div class="test-item">';
    echo '<h3>API文件包含安全检查</h3>';
    
    // 检查api.php的修改
    $api_content = file_get_contents(PHPCMS_PATH . 'api.php');
    if (strpos($api_content, 'allowed_apis') !== false && strpos($api_content, 'realpath') !== false) {
        echo '<div class="success">✅ <strong>修复成功:</strong> API文件包含已加强安全验证</div>';
    } else {
        echo '<div class="danger">❌ <strong>修复失败:</strong> API文件包含安全修复可能未生效</div>';
    }
    
    echo '</div>';
    ?>
    
    <h2>5. 系统安全配置检查</h2>
    <?php
    echo '<div class="test-item">';
    echo '<h3>PHP安全配置检查</h3>';
    
    $security_checks = array(
        'expose_php' => 'Off',
        'display_errors' => 'Off', 
        'allow_url_fopen' => 'Off',
        'allow_url_include' => 'Off'
    );
    
    foreach ($security_checks as $setting => $expected) {
        $current = ini_get($setting);
        if (strtolower($current) == strtolower($expected)) {
            echo '<div class="success">✅ ' . $setting . ': ' . $current . '</div>';
        } else {
            echo '<div class="warning">⚠️ ' . $setting . ': ' . $current . ' (建议: ' . $expected . ')</div>';
        }
    }
    
    // 检查.htaccess文件
    if (file_exists(PHPCMS_PATH . '.htaccess')) {
        echo '<div class="success">✅ <strong>安全配置:</strong> 根目录.htaccess保护文件已创建</div>';
    } else {
        echo '<div class="warning">⚠️ <strong>建议:</strong> 根目录缺少.htaccess保护文件</div>';
    }
    
    echo '</div>';
    ?>
    
    <h2>6. 版本信息</h2>
    <?php
    echo '<div class="test-item info">';
    echo '<h3>当前系统版本</h3>';
    
    $version_config = pc_base::load_config('version');
    if ($version_config) {
        echo '<p><strong>PHPCMS版本:</strong> ' . $version_config['pc_version'] . '</p>';
        echo '<p><strong>发布日期:</strong> ' . $version_config['pc_release'] . '</p>';
        echo '<div class="warning">⚠️ <strong>安全建议:</strong> 当前版本较旧，建议关注官方安全更新</div>';
    }
    
    echo '</div>';
    ?>
    
    <h2>7. 安全建议</h2>
    <div class="test-item info">
        <h3>进一步安全加固建议</h3>
        <ul>
            <li>✅ 定期备份数据库和重要文件</li>
            <li>✅ 监控系统日志，特别是错误日志</li>
            <li>✅ 定期检查上传目录是否有可疑文件</li>
            <li>✅ 考虑部署Web应用防火墙(WAF)</li>
            <li>✅ 定期更新PHP版本和扩展</li>
            <li>✅ 使用HTTPS加密传输</li>
            <li>✅ 限制后台访问IP</li>
            <li>⚠️ <strong>重要:</strong> 测试完成后请删除此测试文件</li>
        </ul>
    </div>
    
    <div class="test-item danger">
        <h3>⚠️ 重要提醒</h3>
        <p><strong>请在测试完成后立即删除此文件 (security_test.php)</strong></p>
        <p>此文件包含系统信息，不应在生产环境中保留。</p>
    </div>
    
    <script>
        // 自动刷新提醒
        setTimeout(function() {
            if (confirm('测试完成！是否现在删除此测试文件？\n\n点击确定将尝试删除文件（需要服务器权限）')) {
                fetch('security_test.php?delete=1')
                    .then(response => response.text())
                    .then(data => {
                        if (data.includes('deleted')) {
                            alert('测试文件已删除');
                            window.location.href = 'index.php';
                        } else {
                            alert('请手动删除 security_test.php 文件');
                        }
                    });
            }
        }, 5000);
    </script>
</body>
</html>

<?php
// 自删除功能
if (isset($_GET['delete']) && $_GET['delete'] == '1') {
    if (unlink(__FILE__)) {
        echo 'File deleted successfully';
    } else {
        echo 'Failed to delete file';
    }
    exit;
}
?>
