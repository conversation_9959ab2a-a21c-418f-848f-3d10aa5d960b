<?php
return array (
  'admin' => 
  array (
    'module' => 'admin',
    'name' => 'admin',
    'url' => '',
    'iscore' => '1',
    'version' => '1.0',
    'description' => '',
    'setting' => 'array (
  \'admin_email\' => \'<EMAIL>\',
  \'adminaccessip\' => \'0\',
  \'maxloginfailedtimes\' => \'8\',
  \'maxiplockedtime\' => \'15\',
  \'minrefreshtime\' => \'2\',
  \'mail_type\' => \'1\',
  \'mail_server\' => \'smtp.qq.com\',
  \'mail_port\' => \'25\',
  \'mail_user\' => \'<EMAIL>\',
  \'mail_auth\' => \'1\',
  \'mail_from\' => \'<EMAIL>\',
  \'mail_password\' => \'\',
  \'errorlog_size\' => \'20\',
)',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2010-10-18',
    'updatedate' => '2010-10-18',
  ),
  'member' => 
  array (
    'module' => 'member',
    'name' => '会员',
    'url' => '',
    'iscore' => '1',
    'version' => '1.0',
    'description' => '',
    'setting' => 'array (
  \'allowregister\' => \'1\',
  \'choosemodel\' => \'1\',
  \'enablemailcheck\' => \'0\',
  \'registerverify\' => \'0\',
  \'showapppoint\' => \'0\',
  \'rmb_point_rate\' => \'10\',
  \'defualtpoint\' => \'0\',
  \'defualtamount\' => \'0\',
  \'showregprotocol\' => \'0\',
  \'regprotocol\' => \'		 欢迎您注册成为phpcms用户
请仔细阅读下面的协议，只有接受协议才能继续进行注册。 
1．服务条款的确认和接纳
　　phpcms用户服务的所有权和运作权归phpcms拥有。phpcms所提供的服务将按照有关章程、服务条款和操作规则严格执行。用户通过注册程序点击“我同意” 按钮，即表示用户与phpcms达成协议并接受所有的服务条款。
2． phpcms服务简介
　　phpcms通过国际互联网为用户提供新闻及文章浏览、图片浏览、软件下载、网上留言和BBS论坛等服务。
　　用户必须： 
　　1)购置设备，包括个人电脑一台、调制解调器一个及配备上网装置。 
　　2)个人上网和支付与此服务有关的电话费用、网络费用。
　　用户同意： 
　　1)提供及时、详尽及准确的个人资料。 
　　2)不断更新注册资料，符合及时、详尽、准确的要求。所有原始键入的资料将引用为注册资料。 
　　3)用户同意遵守《中华人民共和国保守国家秘密法》、《中华人民共和国计算机信息系统安全保护条例》、《计算机软件保护条例》等有关计算机及互联网规定的法律和法规、实施办法。在任何情况下，phpcms合理地认为用户的行为可能违反上述法律、法规，phpcms可以在任何时候，不经事先通知终止向该用户提供服务。用户应了解国际互联网的无国界性，应特别注意遵守当地所有有关的法律和法规。
3． 服务条款的修改
　　phpcms会不定时地修改服务条款，服务条款一旦发生变动，将会在相关页面上提示修改内容。如果您同意改动，则再一次点击“我同意”按钮。 如果您不接受，则及时取消您的用户使用服务资格。
4． 服务修订
　　phpcms保留随时修改或中断服务而不需知照用户的权利。phpcms行使修改或中断服务的权利，不需对用户或第三方负责。
5． 用户隐私制度
　　尊重用户个人隐私是phpcms的 基本政策。phpcms不会公开、编辑或透露用户的注册信息，除非有法律许可要求，或phpcms在诚信的基础上认为透露这些信息在以下三种情况是必要的： 
　　1)遵守有关法律规定，遵从合法服务程序。 
　　2)保持维护phpcms的商标所有权。 
　　3)在紧急情况下竭力维护用户个人和社会大众的隐私安全。 
　　4)符合其他相关的要求。 
6．用户的帐号，密码和安全性
　　一旦注册成功成为phpcms用户，您将得到一个密码和帐号。如果您不保管好自己的帐号和密码安全，将对因此产生的后果负全部责任。另外，每个用户都要对其帐户中的所有活动和事件负全责。您可随时根据指示改变您的密码，也可以结束旧的帐户重开一个新帐户。用户同意若发现任何非法使用用户帐号或安全漏洞的情况，立即通知phpcms。
7． 免责条款
　　用户明确同意网站服务的使用由用户个人承担风险。 　　 
　　phpcms不作任何类型的担保，不担保服务一定能满足用户的要求，也不担保服务不会受中断，对服务的及时性，安全性，出错发生都不作担保。用户理解并接受：任何通过phpcms服务取得的信息资料的可靠性取决于用户自己，用户自己承担所有风险和责任。 
8．有限责任
　　phpcms对任何直接、间接、偶然、特殊及继起的损害不负责任。
9． 不提供零售和商业性服务 
　　用户使用网站服务的权利是个人的。用户只能是一个单独的个体而不能是一个公司或实体商业性组织。用户承诺不经phpcms同意，不能利用网站服务进行销售或其他商业用途。
10．用户责任 
　　用户单独承担传输内容的责任。用户必须遵循： 
　　1)从中国境内向外传输技术性资料时必须符合中国有关法规。 
　　2)使用网站服务不作非法用途。 
　　3)不干扰或混乱网络服务。 
　　4)不在论坛BBS或留言簿发表任何与政治相关的信息。 
　　5)遵守所有使用网站服务的网络协议、规定、程序和惯例。
　　6)不得利用本站危害国家安全、泄露国家秘密，不得侵犯国家社会集体的和公民的合法权益。
　　7)不得利用本站制作、复制和传播下列信息： 
　　　1、煽动抗拒、破坏宪法和法律、行政法规实施的；
　　　2、煽动颠覆国家政权，推翻社会主义制度的；
　　　3、煽动分裂国家、破坏国家统一的；
　　　4、煽动民族仇恨、民族歧视，破坏民族团结的；
　　　5、捏造或者歪曲事实，散布谣言，扰乱社会秩序的；
　　　6、宣扬封建迷信、淫秽、色情、赌博、暴力、凶杀、恐怖、教唆犯罪的；
　　　7、公然侮辱他人或者捏造事实诽谤他人的，或者进行其他恶意攻击的；
　　　8、损害国家机关信誉的；
　　　9、其他违反宪法和法律行政法规的；
　　　10、进行商业广告行为的。
　　用户不能传输任何教唆他人构成犯罪行为的资料；不能传输长国内不利条件和涉及国家安全的资料；不能传输任何不符合当地法规、国家法律和国际法 律的资料。未经许可而非法进入其它电脑系统是禁止的。若用户的行为不符合以上的条款，phpcms将取消用户服务帐号。
11．网站内容的所有权
　　phpcms定义的内容包括：文字、软件、声音、相片、录象、图表；在广告中全部内容；电子邮件的全部内容；phpcms为用户提供的商业信息。所有这些内容受版权、商标、标签和其它财产所有权法律的保护。所以，用户只能在phpcms和广告商授权下才能使用这些内容，而不能擅自复制、篡改这些内容、或创造与内容有关的派生产品。
12．附加信息服务
　　用户在享用phpcms提供的免费服务的同时，同意接受phpcms提供的各类附加信息服务。
13．解释权
　　本注册协议的解释权归phpcms所有。如果其中有任何条款与国家的有关法律相抵触，则以国家法律的明文规定为准。 \',
  \'registerverifymessage\' => \' 欢迎您注册成为phpcms用户，您的账号需要邮箱认证，点击下面链接进行认证：{click}
或者将网址复制到浏览器：{url}\',
  \'forgetpassword\' => \' phpcms密码找回，请在一小时内点击下面链接进行操作：{click}
或者将网址复制到浏览器：{url}\',
)',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2010-09-06',
    'updatedate' => '2010-09-06',
  ),
  'pay' => 
  array (
    'module' => 'pay',
    'name' => '支付',
    'url' => '',
    'iscore' => '1',
    'version' => '1.0',
    'description' => '',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2010-09-06',
    'updatedate' => '2010-09-06',
  ),
  'digg' => 
  array (
    'module' => 'digg',
    'name' => '顶一下',
    'url' => '',
    'iscore' => '0',
    'version' => '1.0',
    'description' => '',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2010-09-06',
    'updatedate' => '2010-09-06',
  ),
  'special' => 
  array (
    'module' => 'special',
    'name' => '专题',
    'url' => '',
    'iscore' => '0',
    'version' => '1.0',
    'description' => '',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2010-09-06',
    'updatedate' => '2010-09-06',
  ),
  'content' => 
  array (
    'module' => 'content',
    'name' => '内容模块',
    'url' => '',
    'iscore' => '1',
    'version' => '1.0',
    'description' => '',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2010-09-06',
    'updatedate' => '2010-09-06',
  ),
  'search' => 
  array (
    'module' => 'search',
    'name' => '全站搜索',
    'url' => '',
    'iscore' => '0',
    'version' => '1.0',
    'description' => '',
    'setting' => 'array (
  \'fulltextenble\' => \'1\',
  \'relationenble\' => \'1\',
  \'suggestenable\' => \'1\',
  \'sphinxenable\' => \'0\',
  \'sphinxhost\' => \'**************\',
  \'sphinxport\' => \'9312\',
)',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2010-09-06',
    'updatedate' => '2010-09-06',
  ),
  'scan' => 
  array (
    'module' => 'scan',
    'name' => '木马扫描',
    'url' => 'scan',
    'iscore' => '0',
    'version' => '1.0',
    'description' => '',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2010-09-01',
    'updatedate' => '2010-09-06',
  ),
  'attachment' => 
  array (
    'module' => 'attachment',
    'name' => '附件',
    'url' => 'attachment',
    'iscore' => '1',
    'version' => '1.0',
    'description' => '',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2010-09-01',
    'updatedate' => '2010-09-06',
  ),
  'block' => 
  array (
    'module' => 'block',
    'name' => '碎片',
    'url' => '',
    'iscore' => '1',
    'version' => '1.0',
    'description' => '',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2010-09-01',
    'updatedate' => '2010-09-06',
  ),
  'collection' => 
  array (
    'module' => 'collection',
    'name' => '采集模块',
    'url' => 'collection',
    'iscore' => '1',
    'version' => '1.0',
    'description' => '',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2010-09-01',
    'updatedate' => '2010-09-06',
  ),
  'dbsource' => 
  array (
    'module' => 'dbsource',
    'name' => '数据源',
    'url' => '',
    'iscore' => '1',
    'version' => '',
    'description' => '',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2010-09-01',
    'updatedate' => '2010-09-06',
  ),
  'template' => 
  array (
    'module' => 'template',
    'name' => '模板风格',
    'url' => '',
    'iscore' => '1',
    'version' => '1.0',
    'description' => '',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2010-09-01',
    'updatedate' => '2010-09-06',
  ),
  'release' => 
  array (
    'module' => 'release',
    'name' => '发布点',
    'url' => '',
    'iscore' => '1',
    'version' => '1.0',
    'description' => '',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2010-09-01',
    'updatedate' => '2010-09-06',
  ),
  'video' => 
  array (
    'module' => 'video',
    'name' => '视频库',
    'url' => '',
    'iscore' => '0',
    'version' => '1.0',
    'description' => '',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2012-09-28',
    'updatedate' => '2012-09-28',
  ),
  'announce' => 
  array (
    'module' => 'announce',
    'name' => '公告',
    'url' => 'announce/',
    'iscore' => '0',
    'version' => '1.0',
    'description' => '公告',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2025-08-15',
    'updatedate' => '2025-08-15',
  ),
  'comment' => 
  array (
    'module' => 'comment',
    'name' => '评论',
    'url' => 'comment/',
    'iscore' => '0',
    'version' => '1.0',
    'description' => '评论',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2025-08-15',
    'updatedate' => '2025-08-15',
  ),
  'link' => 
  array (
    'module' => 'link',
    'name' => '友情链接',
    'url' => '',
    'iscore' => '0',
    'version' => '1.0',
    'description' => '',
    'setting' => 'array (
  1 => 
  array (
    \'is_post\' => \'1\',
    \'enablecheckcode\' => \'0\',
  ),
)',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2010-09-06',
    'updatedate' => '2010-09-06',
  ),
  'vote' => 
  array (
    'module' => 'vote',
    'name' => '投票',
    'url' => '',
    'iscore' => '0',
    'version' => '1.0',
    'description' => '',
    'setting' => 'array (
  1 => 
  array (
    \'default_style\' => \'default\',
    \'vote_tp_template\' => \'vote_tp\',
    \'allowguest\' => \'1\',
    \'enabled\' => \'1\',
    \'interval\' => \'1\',
    \'credit\' => \'1\',
  ),
)',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2010-09-06',
    'updatedate' => '2010-09-06',
  ),
  'message' => 
  array (
    'module' => 'message',
    'name' => '短消息',
    'url' => '',
    'iscore' => '0',
    'version' => '1.0',
    'description' => '',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2010-09-06',
    'updatedate' => '2010-09-06',
  ),
  'mood' => 
  array (
    'module' => 'mood',
    'name' => '新闻心情',
    'url' => 'mood/',
    'iscore' => '0',
    'version' => '1.0',
    'description' => '新闻心情',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2025-08-15',
    'updatedate' => '2025-08-15',
  ),
  'poster' => 
  array (
    'module' => 'poster',
    'name' => '广告模块',
    'url' => 'poster/',
    'iscore' => '0',
    'version' => '1.0',
    'description' => '广告模块',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2025-08-15',
    'updatedate' => '2025-08-15',
  ),
  'formguide' => 
  array (
    'module' => 'formguide',
    'name' => '表单向导',
    'url' => 'formguide/',
    'iscore' => '0',
    'version' => '1.0',
    'description' => 'formguide',
    'setting' => 'array (
  \'allowmultisubmit\' => \'1\',
  \'interval\' => \'30\',
  \'allowunreg\' => \'0\',
  \'mailmessage\' => \'用户向我们提交了表单数据，赶快去看看吧。\',
)',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2010-10-20',
    'updatedate' => '2010-10-20',
  ),
  'wap' => 
  array (
    'module' => 'wap',
    'name' => '手机门户',
    'url' => 'wap/',
    'iscore' => '0',
    'version' => '1.0',
    'description' => '手机门户',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2025-08-15',
    'updatedate' => '2025-08-15',
  ),
  'upgrade' => 
  array (
    'module' => 'upgrade',
    'name' => '在线升级',
    'url' => '',
    'iscore' => '0',
    'version' => '1.0',
    'description' => '',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2011-05-18',
    'updatedate' => '2011-05-18',
  ),
  'tag' => 
  array (
    'module' => 'tag',
    'name' => '标签向导',
    'url' => 'tag/',
    'iscore' => '0',
    'version' => '1.0',
    'description' => '标签向导',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2025-08-15',
    'updatedate' => '2025-08-15',
  ),
  'sms' => 
  array (
    'module' => 'sms',
    'name' => '短信平台',
    'url' => 'sms/',
    'iscore' => '0',
    'version' => '1.0',
    'description' => '短信平台',
    'setting' => '',
    'listorder' => '0',
    'disabled' => '0',
    'installdate' => '2011-09-02',
    'updatedate' => '2011-09-02',
  ),
);
?>