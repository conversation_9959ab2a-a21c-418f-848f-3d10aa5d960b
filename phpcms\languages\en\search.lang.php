<?php
$LANG['add_search_type'] = 'Add search type';

$LANG['select_model_name'] = 'Model';
$LANG['select_module_name'] = 'Module';
$LANG['type_name'] = 'Type name';
$LANG['description'] = 'Description';

$LANG['re_index_note'] = '1.Rebuilding the search index will be wiped clean all cached searching.';
$LANG['tiao'] = 'items';
$LANG['confirm_reindex'] = 'Confirm to rebuild indexes';
$LANG['basic_setting'] = 'Basic settings';
$LANG['sphinx_setting'] = 'Sphinx configuration';
$LANG['enable_search'] = 'Enable site-wide search';
$LANG['relationenble'] = 'Enable related search';
$LANG['relationenble_note'] = '(This function would cause an extra load on database)';
$LANG['suggestenable'] = 'Enable search suggestions';
$LANG['suggestenable_note'] = '(show data sources google)';
$LANG['sphinxenable'] = 'Enable sphinx full text search';
$LANG['host'] = 'Server host address';
$LANG['port'] = 'Server port';
$LANG['testing'] = 'Testing...';
$LANG['testsuccess'] = 'Connecting successfully';
$LANG['hostempty'] = 'Hostname is required';
$LANG['portempty'] = 'Port is required';
$LANG['test'] = 'Test';
$LANG['sort'] = 'Ordering';
$LANG['catname'] = 'Category name';
$LANG['modulename'] = 'Module';
$LANG['modlename'] = 'Model';
$LANG['catdescription'] = 'Category description ';
$LANG['opreration'] = 'Operation';
$LANG['modify'] = 'Modify';
$LANG['content_module'] = 'Content module';
$LANG['edit_cat'] = 'Edit category';
$LANG['searh_notice'] = 'Warning: full text index module requires that mysql enables full text index function. How to enable? Edit mysql configuration file -> Set window server to my.ini -> Set linux server to my.cnf -> Add a line "ft_min_word_len=1" after [mysqld] -> restart mysql
';
?>