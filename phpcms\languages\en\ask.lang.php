<?php

/*Language Format:
Add a new file(.lang.php) with your module name at /phpcms/languages/
translation save at the array:$LANG
*/
$LANG['announce_manage_add'] = 'announce_manage_add';
$LANG['startdate'] = 'startdate';
$LANG['enddate'] = 'enddate';
$LANG['inputer'] = 'inputer';
$LANG['cancel_selected'] = 'cancel_selected';
$LANG['pass_selected'] = 'pass_selected';
$LANG['remove_selected'] = 'remove_selected';
$LANG['announce_title'] = 'announce_title';
$LANG['announce_content'] = 'announce_content';
$LANG['announce_status'] = 'announce_status';
$LANG['input_announce_title'] = 'input_announce_title';
$LANG['select_stardate'] = 'select_stardate';
$LANG['select_downdate'] = 'select_downdate';
$LANG['title_cannot_empty'] = 'title_cannot_empty';
$LANG['title_min_3_chars'] = 'title_min_3_chars';
$LANG['right'] = 'right!';
$LANG['confirm_delete'] = 'confirm_delete?';
$LANG['announce_deleted'] = 'announce_deleted';
$LANG['announce_passed'] = 'announce_passed!';
$LANG['manage_category'] = 'manage_category';
$LANG['import_category'] = 'import_category';
$LANG['category_cache_tips'] = 'category_cache_tips';
$LANG['catid'] = 'catid';
$LANG['catname'] = 'catname';
$LANG['vistor'] = 'vistor';
$LANG['add_sub_category'] = 'add_sub_category';
$LANG['add_category_types'] = 'add_category_types';
$LANG['normal_add'] = 'normal_add';
$LANG['parent_category'] = 'parent_category';
$LANG['catgory_img'] = 'catgory_img';
$LANG['description'] = 'description';
$LANG['meta_title'] = 'meta_title';
$LANG['meta_keywords'] = 'meta_keywords';
$LANG['meta_description'] = 'meta_description';	
$LANG['author'] = 'author';
$LANG['quetion_name'] = 'quetion_name';
$LANG['add_time'] = 'add_time';
$LANG['reward'] = 'reward';
$LANG['answercount'] = 'answercount';
$LANG['cancel_recommond'] = 'cancel_recommond';
$LANG['pass_recommond'] = 'pass_recommond';
$LANG['index'] = 'index';
$LANG['category_add'] = 'category_add';
$LANG['yp_menu'] = 'yp_menu';
$LANG['all_question'] = 'all_question';
$LANG['reply'] = 'reply';
$LANG['at'] = 'at';
$LANG['release'] = 'release';
$LANG['support'] = 'support';
$LANG['my_ask'] = 'my_ask';
$LANG['ask_info'] = 'ask_info';
$LANG['actor'] = 'actor';
$LANG['ask_count'] = 'ask_count';
$LANG['reply_count'] = 'reply_count';
$LANG['my_question'] = 'my_question';
$LANG['title'] = 'title';
$LANG['addtime'] = 'addtime';
$LANG['my_reply'] = 'my_reply';
$LANG['original_title'] = 'original_title';
$LANG['actor_setting'] = 'actor_setting';
$LANG['setting'] = 'setting';
$LANG['role_name'] = 'role_name';
$LANG['grade'] = 'grade';
$LANG['Integral'] = 'Integral';
$LANG['edit_actor'] = 'edit_actor';
$LANG['add_actor_succ'] = 'add_actor_succ';
$LANG['actor_del_cofirm'] = 'actor_del_cofirm';
$LANG['actor_del_succ'] = 'actor_del_succ';
$LANG['please_select_parent_category'] = 'please_select_parent_category';
$LANG['batch_add_tips'] = 'batch_add_tips';
$LANG['wonderful'] = 'wonderful';
$LANG['no_wonderful'] = 'no_wonderful';
$LANG['category_successfull'] = 'category_successfull';
$LANG['my_team'] = 'my_team';
$LANG['team_manage'] = 'team_manage';
$LANG['save_setting'] = 'save_setting';
$LANG['team_member'] = 'team_member';
$LANG['all_score'] = 'all_score';
$LANG['accept_num'] = 'accept_num';
$LANG['answer_num'] = 'answer_num';
$LANG['base_setting'] = 'base_setting';
$LANG['anony_integral'] = 'anony_integral';
$LANG['answer_integral'] = 'answer_integral';
$LANG['right_answer_integral'] = 'right_answer_integral';
$LANG['vote_integral'] = 'vote_integral';
$LANG['delete_ask_integral'] = 'delete_ask_integral';
$LANG['delete_answer_integral'] = 'delete_answer_integral';
$LANG['question_is_check'] = 'question_is_check';
$LANG['team_name'] = 'team_name';
$LANG['teamer'] = 'teamer';
$LANG['declaration'] = 'declaration';
$LANG['team_category'] = 'team_category';
$LANG['team_del_cofirm'] = 'team_del_cofirm';
$LANG['team_del_succ'] = 'team_del_succ';
$LANG['team_category_select'] = 'team_category_select';
$LANG['tuan'] = 'tuan';
$LANG['futuan'] = 'futuan';
$LANG['star'] = 'star';
$LANG['member_level'] = 'member_level';
$LANG['set_member_level'] = 'set_member_level';
$LANG['no_level'] = 'no_level';
$LANG['members_del_succ'] = 'members_del_succ';
$LANG['team_member_tips'] = 'team_member_tips';
$LANG['team_recommend'] = 'team_recommend';
$LANG['team_recommend_confirm'] = 'team_recommend_confirm?';
$LANG['cancel_recommend'] = 'cancel_recommend';
$LANG['register'] = 'register';
$LANG['login'] = 'login';
$LANG['member_center'] = 'member_center';
$LANG['logout'] = 'logout';
$LANG['please_login_operation'] = 'please_login_operation';
$LANG['reply_success'] = 'reply_success';
$LANG['reply_edit_success'] = 'reply_edit_success';
$LANG['announce_expired'] = 'announce_expired';
$LANG['add_part_notes'] = 'add_part_notes';
$LANG['ask_center'] = 'ask_center';
$LANG['ask_index'] = 'ask_index';
$LANG['my_comment'] = 'my_comment';
$LANG['quesion_title'] = 'quesion_title';
$LANG['select_category_menu'] = 'select_category_menu';
$LANG['view_reply'] = 'view_reply';
$LANG['reply_contents'] = 'reply_contents';
$LANG['comment'] = 'comment';
$LANG['comment_contents'] = 'comment_contents';
$LANG['comment_answer'] = 'comment_answer';
$LANG['delete_success'] = 'delete_success';
$LANG['recommond_canceled'] = 'recommond_canceled';
$LANG['recommond_passed'] = 'recommond_passed';
$LANG['selected_not_null'] = 'selected_not_null';
$LANG['you_do_not_check'] = 'you_do_not_check';
$LANG['join_success'] = 'join_success';
$LANG['exits_members'] = 'exits_members';
$LANG['link_have_error'] = 'link_have_error';
$LANG['operation'] = 'operation';
$LANG['team_max_remind'] = 'team_max_remind';
$LANG['team_name'] = 'team_name';
$LANG['create_time'] = 'create_time';
$LANG['loginout_success'] = 'loginout_success';
$LANG['quit_team_success'] = 'quit_team_success';
$LANG['confirm_quit_team'] = 'confirm_quit_team?';
$LANG['team_dissolve_success'] = 'team_dissolve_success';
$LANG['audit'] = 'audit';
$LANG['audit_pass_members'] = 'audit_pass_members';
$LANG['point_not_enough'] = 'point_not_enough';
$LANG['edit_question_success'] = 'edit_question_success';
$LANG['question_already_reply'] = 'question_already_reply';
$LANG['not_reply_self_question'] = 'not_reply_self_question';
