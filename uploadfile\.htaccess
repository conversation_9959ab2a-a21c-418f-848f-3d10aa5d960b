# 上传目录安全配置
# 禁止执行PHP文件

# 禁止执行所有脚本文件
<FilesMatch "\.(php|php3|php4|php5|phtml|pht|pl|py|jsp|asp|aspx|sh|cgi)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# 只允许特定的安全文件类型
<FilesMatch "\.(jpg|jpeg|png|gif|bmp|pdf|doc|docx|xls|xlsx|ppt|pptx|txt|zip|rar|7z)$">
    Order allow,deny
    Allow from all
</FilesMatch>

# 禁止目录浏览
Options -Indexes

# 禁止访问隐藏文件
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# 设置正确的MIME类型
<IfModule mod_mime.c>
    AddType image/jpeg .jpg .jpeg
    AddType image/png .png
    AddType image/gif .gif
    AddType application/pdf .pdf
    AddType application/msword .doc
    AddType application/vnd.openxmlformats-officedocument.wordprocessingml.document .docx
    AddType application/vnd.ms-excel .xls
    AddType application/vnd.openxmlformats-officedocument.spreadsheetml.sheet .xlsx
    AddType text/plain .txt
    AddType application/zip .zip
    AddType application/x-rar-compressed .rar
</IfModule>

# 防止执行权限
<IfModule mod_headers.c>
    Header set X-Content-Type-Options nosniff
    Header set X-Frame-Options DENY
</IfModule>
