{template 'member', 'header'}
<script type="text/javascript">
<!--
$(function(){
	$.formValidator.initConfig({autotip:true,formid:"myform",onerror:function(msg){}});
	$("#subject").formValidator({empty:true,onshow:"标题，可以为空",onfocus:"请输入标题",oncorrect:"输入正确！",onempty:"不填替标题了吗？"}).inputValidator({min:1,onerror:"请输入标题"});
	$("#con").formValidator({onshow:"请输入内容",onfocus:"内容不能为空"}).inputValidator({min:1,max:999,onerror:"内容不能为空"});
	$("#code").formValidator({onshow:"请输入验证码",onfocus:"验证码不能为空"}).inputValidator({min:1,max:999,onerror:"验证码不能为空"}).ajaxValidator({
	    type : "get",
		url : "",
		data :"m=pay&c=deposit&a=public_checkcode",
		datatype : "html",
		async:'false',
		success : function(data){	
            if(data == 1)
			{
                return true;
			}
            else
			{
                return false;
			}
		},
		buttons: $("#dosubmit"),
		onerror : "验证码错误",
		onwait : "验证中"
	});
})
//-->
</script>
<div id="memberArea">
{template 'member', 'left'}
<div class="col-auto">
<div class="col-1 ">
<h6 class="title">系统短消息记录</h6>
<div class="content">
 <table width="100%" cellspacing="0" class="table_form">
 
     <tr>
       <th><div align=center><img src="{IMG_PATH}member/nophoto.gif" width="50" height="50"><br><font color=red>{$infos['send_from_id']}</font></div></th>
       <td><b>{$infos['subject']}</b> - {date('Y-m-d H:i:s',$infos['inputtime'])}<br><br>{nl2br($infos['content'])}</td>
     </tr>
     
     {loop $reply_infos $reply_info}  
     <tr>
       <th><div align=center><img src="{IMG_PATH}member/nophoto.gif" width="50" height="50"></img><br>{$reply_info['send_from_id']}</div></th>
       <td>{$reply_info['subject']} - {date('Y-m-d H:i:s',$reply_info['message_time'])}<br><br></br><b>{$reply_info['content']}</b></td>
     </tr> 
     {/loop}
     
     
         </label></td>
     </tr>
   </table>
    </div>
   <span class="o1"></span><span class="o2"></span><span class="o3"></span><span class="o4"></span>
   </div>
<div class="bk10"></div>
   </div>
</div> 
{template 'member', 'footer'}