<?php
$LANG['node_add'] = 'Add collecting point';
$LANG['nodename'] = 'Name';
$LANG['modelname'] = 'Model name';
$LANG['charset'] = 'Charset';
$LANG['content_num'] = 'Number of post';
$LANG['operation'] = 'Operation';
$LANG['adddate'] = 'Add date';
$LANG['lastdate'] = 'Last data collected time';
$LANG['sure_delete'] = 'Are you sure you want to delete?';
$LANG['basic_configuration'] = 'Basic settings';
$LANG['more_configuration'] = 'More info';
$LANG['urlslist'] = 'URL list';
$LANG['sequence'] = 'URL sequence';
$LANG['multiple_pages'] = 'Multiple pages';
$LANG['single_page'] = 'Single page';
$LANG['select_model'] = 'Please select model';
$LANG['not_to_collect'] = 'No data to collect';
$LANG['url_collect_msg'] = 'URL list Not Found. Please continue to collect URL';
$LANG['collectioning'] = 'Processing:';
$LANG['collection_success'] = 'Collecting completely';
$LANG['please_select_cat'] = 'Please select column';
$LANG['illegal_section_parameter'] = 'Invalid column parameter';
$LANG['model_does_not_exist_please_update_the_cache_model'] = 'Model does not exist. Please update model cache';
$LANG['please_choose'] = 'Please select';
$LANG['author'] = 'Author';
$LANG['comeform'] = 'Source';
$LANG['time'] = 'Time';
$LANG['content'] = 'Content';
$LANG['node_not_found'] = 'Collection node not found';
$LANG['article_was_imported'] = 'Articles were imported.';
$LANG['are_imported_the_import_process'] = 'Importing:';
$LANG['url_list'] = 'URL list';
$LANG['in_all'] = 'Total';
$LANG['all_count_msg'] = 'Records. Duplicate records';
$LANG['import_num_msg'] = '. Removing duplicate records. The number of total records is';
$LANG['following_operation'] = 'You can continue to';
$LANG['following_operation_1'] = '1. Start to collect the content of article';
$LANG['following_operation_2'] = '2. Back to Data Collecting Point Manager';
$LANG['the_new_publication_solutions'] = 'Add publishing solution';
$LANG['category'] = 'Column';
$LANG['the_withdrawal_of_the_summary'] = 'Automatic summary extraction';
$LANG['if_the_contents_of_intercepted'] = 'Get content';
$LANG['characters_to_a_summary_of_contents'] = 'Characters to summary extraction';
$LANG['the_withdrawal_of_thumbnails'] = 'Get thumbnail image automatically';
$LANG['whether_access_to_the_content_of'] = 'Get the';
$LANG['picture_a_caption_pictures'] = 'image as header images from content';
$LANG['import_article_state'] = 'Status';
$LANG['pendingtrial'] = 'Pending';
$LANG['fantaoboys'] = 'Approved';
$LANG['corresponding_labels_and_a_database_ties'] = 'Database - Relationship with Tags';
$LANG['the_original_database_field'] = 'Original database field';
$LANG['explain'] = 'Database field description';
$LANG['label_field__collected_with_the_result_'] = 'Tags field (Collecting result)';
$LANG['publish_the_list'] = 'The list of publishing solutions';
$LANG['had_collected_from_the_roll'] = 'Original collecting name';
$LANG['the_new_gathering'] = 'New collecting name';
$LANG['url_rewrites'] = 'URL rule';
$LANG['content_rules'] = 'Content rule';
$LANG['custom_rule'] = 'Custom rule';
$LANG['eigrp'] = 'Advanced settings';
$LANG['collection_items_of'] = 'Project name';
$LANG['encode_varchar'] = 'Encode';
$LANG['web_sites_to_collect'] = 'URL collecting';
$LANG['url_type'] = 'URL type';
$LANG['url_configuration'] = 'URL settings';
$LANG['test'] = 'Test';
$LANG['url_msg'] = '(For example, http://www.phpcms.cn/help/rumen/(*).html. Use <a href="javascript:insertText(\'urlpage_1\', \'(*)\')">(*)</a>as a wildcard.';
$LANG['page_from'] = 'Page from';
$LANG['from'] = 'From';
$LANG['to'] = 'To';
$LANG['finish'] = 'End';
$LANG['increment_by'] = 'Increments at a time';
$LANG['one_per_line'] = 'One item per line';
$LANG['site_must_contain'] = 'URL must contain';
$LANG['the_web_site_does_not_contain'] = 'URL must NOT contain';
$LANG['base_configuration'] = 'Base Configuration';
$LANG['base_msg'] = 'You have to set up Base if target website is using Base.';
$LANG['get_url'] = 'Get URL';
$LANG['rule_msg'] = '1. Please clearly define where a block of matching rule starts and ends, [content] as a wildcard.<BR> 2. Format for filtering options is “Unwanted contents[|]Replacement”. Unwanted contents support regular expressions. One item per line<BR>';
$LANG['expand_all'] = 'Expand all';
$LANG['all_the'] = 'Closed all';
$LANG['rule'] = 'Rule';
$LANG['matching_rule'] = 'Matching rule ';
$LANG['[content]'] = '[content]';
$LANG['use'] = 'Use';
$LANG['w_wildmenu'] = 'As wildcard';
$LANG['filtering'] = 'Filtering options ';
$LANG['select'] = 'Select';
$LANG['content_page'] = 'Content pagebreak';
$LANG['page_mode'] = 'Pagebreak mode';
$LANG['all_are_models'] = 'Display all';
$LANG['down_the_pages_mode'] = 'Page Up/Down mode';
$LANG['nextpage_rule'] = 'Next page rule';
$LANG['nextpage_rule_msg'] = 'Please input the code of Next page Hyperlink. For example, <a href="http://www.phpcms.cn/page_1.html"> Next page</a>, “Next page rule” is “Next page”';
$LANG['add_item'] = 'Add project';
$LANG['rulename'] = 'Rule name';
$LANG['rules_in_english'] = 'Rule name';
$LANG['download_pic'] = 'Download picture';
$LANG['no_download'] = 'Cannot download';
$LANG['watermark'] = 'Image watermark';
$LANG['gfl_sdk'] = 'Enable watermark';
$LANG['no_gfl_sdk'] = 'No watermark';
$LANG['content_page_models'] = 'Content pagebreak';
$LANG['no_page'] = 'No pagebreak';
$LANG['by_the_paging'] = 'Pagebreak based on original post';
$LANG['sort_order'] = 'Ordering';
$LANG['with_goals_from_the_same'] = 'Same as target website';
$LANG['and_objectives_of_the_standing_opposite'] = 'Opposite to target website';
$LANG['testpageurl'] = 'Test URL';
$LANG['invert'] = 'Inverse';
$LANG['select_all'] = 'Select all';
$LANG['collect_call'] = 'Collecting point name';
$LANG['cfg'] = 'Config files';
$LANG['only_support_txt_file_upload'] = 'Only support .txt file';
$LANG['collection_web_site'] = 'Collecting URL';
$LANG['collection_content'] = 'Collecting content';
$LANG['public_content'] = 'Publishing content';
$LANG['copy'] = 'Copy';
$LANG['export'] = 'Export';
$LANG['import_collection_points'] = 'Import';
$LANG['data_acquisition_testdat'] = 'Test';
$LANG['copy_node'] = 'Copy';
$LANG['import_collection_points'] = 'Import';
$LANG['view'] = 'View';
$LANG['loading'] = 'Loading...';
$LANG['content_view'] = 'View';
$LANG['content_list'] = 'Article list';
$LANG['all'] = 'Total';
$LANG['if_bsnap_then'] = 'Not Collected';
$LANG['spidered'] = 'Collected';
$LANG['imported'] = 'Imported';
$LANG['status'] = 'Status';
$LANG['url'] = 'URL';
$LANG['also_delete_the_historical'] = 'Delete history';
$LANG['import_selected'] = 'The selected Import';
$LANG['import_all'] = 'Import all';
$LANG['select_article'] = 'Please select article';
$LANG['program_add_operation_success'] = 'Added successfully';
$LANG['handler_functions'] = 'handler function';
?>