<?php

/*Language Format:
Add a new file(.lang.php) with your module name at /phpcms/languages/
translation save at the array:$LANG
*/
$LANG['special']							=	'Topics';
$LANG['title_cannot_empty']					=	'Topic title is required';
$LANG['add_special']						=	'Add topic';
$LANG['add_special_success']				=	'Topic has been saved successfully!';
$LANG['edit_special_success']				=	'Topic has been changed successfully!';
$LANG['select_type']						=	'Please choose a destination category';	
$LANG['import_success']						=	'Imported successfully! System will automatically filter replicate information.';
$LANG['select_model']						=	'Please select module';
$LANG['please_in_admin']					=	'Please perform this operation in backstage';
$LANG['banner_no_empty']					=	'Banner URL is required';
$LANG['thumb_no_empty']						=	'Thumbnail image is required';
$LANG['content_add_success']				=	'Content added successfully';
$LANG['content_edit_success']				=	'Content edited successfully';
$LANG['add_content']						=	'Add content';
$LANG['import_content']						=	'Import content';
$LANG['title_no_empty']						=	'Title is required';
$LANG['no_select_type']						=	'Please choose the categories your thematic content will be displayed in';
$LANG['content_no_empty']					=	'Content is required';
$LANG['special_not_exist']					=	'The topic does not exist or has been marked for deletion';
$LANG['content_checking']					=	'The content does not exist or not yet verified';
$LANG['special_setting']					=	'Topic settings';
$LANG['belong_category']					=	'Category';
$LANG['special_title']						=	'Topic name';
$LANG['special_banner']						=	'Banner';
$LANG['sepcial_thumb']						=	'Thumbnail image';
$LANG['special_intro']						=	'Topic guide';
$LANG['ishtml']								=	'Generate static page';
$LANG['special_filename']					=	'Make directory';
$LANG['submit_no_edit']						=	'Please confirm the information. Once submitted, it cannot be modified';
$LANG['index_page']							=	'Page break on Home page';
$LANG['special_template']					=	'Topic template';
$LANG['special_content_template']			=	'Content page template';
$LANG['special_status']						=	'Status';
$LANG['open']								=	'Public';
$LANG['pause']								=	'Suspend';
$LANG['special_type_setting']				=	'Classification';
$LANG['special_type']						=	'Subcategory';
$LANG['type_id']							=	'ID';
$LANG['type_name']							=	'Category name';
$LANG['type_path']							=	'Path';
$LANG['pics_news']							=	'News photos';
$LANG['choose_pic_news']					=	'Choose an article';
$LANG['choose_pic_model']					=	'Please choose all the information related to a model of picture';
$LANG['add_vote']							=	'Add to poll';
$LANG['choose_exist_vote']					=	'Select an existing poll';
$LANG['choose_vote']						=	'Select poll';
$LANG['extend_setting']						=	'Advance settings';
$LANG['add_new_vote']						=	'Add new poll';
$LANG['remove']								=	'Remove';
$LANG['special_type_template']				=	'Category page template';
$LANG['please_input_special_title']			=	'Please input a topic name';
$LANG['min_3_title']						=	'Cannot be less than 6 characters';
$LANG['true']								=	'Correct';
$LANG['server_no_data']						=	'Server busy. Please retry';
$LANG['special_exist']						=	'This topic already exists';
$LANG['checking']							=	'Checking validation...';
$LANG['please_upload_banner']				=	'Please upload a topic banner';
$LANG['please_upload_thumb']				=	'Please upload a topic thumbnail image';
$LANG['special_file']						=	'Please input a filename so as to generate static page.';
$LANG['use_letters']						=	'Please ONLY contain letters, digits or underscores';
$LANG['please_input_name']					=	'Please input filename';
$LANG['error']								=	'Bad input';
$LANG['for_type']							=	'Category';
$LANG['keyword']							=	'keywords';
$LANG['filename_exist']						=	'the filename already exists';
$LANG['input_type_name']					=	'Please input category name';
$LANG['input_type_path']					=	'Please input category path';

//批量更新
$LANG['update_special_success']				=	'Topic updated successfully';
$LANG['update_success']						=	'Updated successfully';
$LANG['index_update_success']				=	'Homepage updated successfully';
$LANG['type_update_success']				=	'Category page updated successfully';
$LANG['type_from']							=	'Category, from <font color="red">';
$LANG['type_end']							=	'</font> to <font color="red">';
$LANG['start_update_type']					=	'Start updating category page';
$LANG['content_update_success']				=	'Content page updated successfully';
$LANG['content_from']						=	'Content from';
$LANG['index_create_finish']				=	'Topic page updated successfully';

//添加、修改信息
$LANG['pos_info']							=	'Current location: Topic> Content manage system> Add information';
$LANG['for_type']							=	'Category';
$LANG['cancel_thumb']						=	'Delete image';
$LANG['please_choose_type']					=	'Please choose category';
$LANG['content_title']						=	'Title';
$LANG['check_exist']						=	'Checking for duplicate';
$LANG['title_exist']						=	'Duplicate title';
$LANG['title_no_exist']						=	'No-repeat title';
$LANG['keywords']							=	'Key words';
$LANG['more_keywords_with_blanks']			=	'Multiple words should be separated with space or “,”';
$LANG['description']						=	'Summary';
$LANG['content']							=	'Content';
$LANG['iscutcontent']						=	'Capture content?';
$LANG['characters_to_contents'] 			=	'Character to content summary';
$LANG['iscutcotent_pic']					=	'Get content the ';
$LANG['picture2thumb']						=	'picture as headline picture';
$LANG['paginationtype'] 					=	'Pagebreak';
$LANG['no_page']							=	'Page break disabled';
$LANG['collate_copies'] 					=	'Auto page break';
$LANG['manual_page'] 						=	'Manual page break';
$LANG['number_of_characters'] 				=	'Characters, including HTML tags';
$LANG['content_thumb']						=	'Thumbnail image';
$LANG['file_upload']						=	'Attachments';
$LANG['author']								=	'Author';
$LANG['islink']								=	'Redirected';
$LANG['inputtime']							=	'Published time';
$LANG['template_style']						=	'Available style';
$LANG['show_template']						=	'Content page template';
$LANG['save']								=	'Save';
$LANG['save_and_add']						=	'Save and Continue to publish';
$LANG['close']								=	'Close';
$LANG['left_will_missing_data'] 			=	'Data will be lost when session closed.';
$LANG['please_choose_type']					=	'Please choose category';
$LANG['please_input_title']					=	'Please input title';
$LANG['content_empty']						=	'Content is required';
$LANG['edit_pos_info']						=	'Current location: Topic> Content manage system> Edit information';

//文章列表
$LANG['listorder']							=	'Ordering';
$LANG['hits']								=	'Hits';
$LANG['inputman']							=	'Publisher';
$LANG['update_time']						=	'Updated time';
$LANG['content_edit']						=	'Edit';
$LANG['special_list']						=	'List of topic';
$LANG['input_time']							=	'Published Time';
$LANG['search']								=	'Search';

//信息导入
$LANG['import']								=	'Import';
$LANG['choose_news']						=	'Please select import data';

//专题列表
$LANG['special_info']						=	'Topic info';
$LANG['create_man']							=	'Created by';
$LANG['create_time']						=	'Created Time';
$LANG['add_news']							=	'Add';
$LANG['import_news']						=	'Import';
$LANG['manage_news']						=	'Manage';
$LANG['template_manage']					=	'Maintenance';
$LANG['elite_special']						=	'Promoted topic';
$LANG['remove_elite']						=	'Demote';
$LANG['special_comment']					=	'Topic comments';
$LANG['edit_special']						=	'Edit topic';
$LANG['del_special']						=	'delete topic';
$LANG['update']								=	'Update';
$LANG['see_comment']						=	'View comments';

//维护模板
$LANG['quit_diy']							=	'Quit';
$LANG['save_edit']							=	'Save';
$LANG['preview_diy']						=	'Preview Effect';
$LANG['preview']							=	'Preview';
$LANG['custom']								=	'Custom';
$LANG['upload_background']					=	'Upload background image';
$LANG['remove_background']					=	'Cancel background image';
$LANG['restore_original_skin']				=	'Reset to default theme';
$LANG['change']								=	'Change';
$LANG['repeat_mode']						=	'Page tile';
$LANG['no-repeat']							=	'Apply';
$LANG['repeat']								=	'Tile';
$LANG['repeat-x']							=	'Horizontal';
$LANG['repeat-y']							=	'Vertical';
$LANG['photo_place']						=	'Location';
$LANG['background_scroll']					=	'Scrolling a background';
$LANG['scroll']								=	'Scroll';
$LANG['fixed']								=	'Fixed';
$LANG['bgcolor']							=	'Background color';
$LANG['text_color']							=	'Text color';
$LANG['link_color']							=	'Link color';
$LANG['pc_tag']								=	'PHPCMS tag';
$LANG['show_diy']							=	'Show DIY';

//clssess
$LANG['please_choose_special']				=	'Please select a topic';
$LANG['content_list']						=	'Table of content';
$LANG['hits_order']							=	'Sorting and Browsing';
$LANG['sitename']							=	'Site name';
$LANG['iselite']							=	'Promote to front page?';
$LANG['get_thumb']							=	'Get thumbnail pictures';
$LANG['order_type']							=	'Ordering';
$LANG['id_asc']								=	'ID Ascending';
$LANG['id_desc']							=	'ID Descending';
$LANG['order_asc']							=	'Ordering ascending';
$LANG['order_desc']							=	'Ordering descending';
$LANG['special_id']							=	'Topic ID';
$LANG['total']								=	'Total';
$LANG['yesterday']							=	'Yesterday';
$LANG['today']								=	'Today';
$LANG['week']								=	'This week';
$LANG['month']								=	'This month';
$LANG['please_upload_thumb']				=	'Please first upload thumbnail pictures';
$LANG['crop_thumb']							=	'Crop';
?>