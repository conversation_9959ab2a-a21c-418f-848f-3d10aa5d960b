<?php
$LANG['operation'] = '操作';
$LANG['view'] = '查看';
$LANG['access'] = '访问';
$LANG['upgrade'] = '在线升级';
$LANG['checkfile'] = '文件校验';
$LANG['updatetime'] = '更新日期';
$LANG['updatelist'] = '可升级版本列表';
$LANG['currentversion'] = '当前版本';
$LANG['lastversion'] = '为最新版';
$LANG['covertemplate'] = '覆盖模版？';


$LANG['view_code'] = '查看代码';
$LANG['modifyfile'] = '修改过的文件';
$LANG['lastmodifytime'] = '最后修改时间';
$LANG['modifyedfile'] = '被修改文件';
$LANG['lostfile'] = '丢失文件';
$LANG['unknowfile'] = '未知文件';
$LANG['filesize'] = '文件大小';
$LANG['begin_checkfile'] = '开始校验文件，请稍候';
$LANG['begin_upgrade'] = '开始升级';
$LANG['upgradeing'] = '正在升级';
$LANG['upgrade_success'] = '升级成功！';
$LANG['lost'] = '丢失';
$LANG['file_address'] = '文件地址';
$LANG['please_check_filepri'] = '复制文件失败，请检查目录权限';
$LANG['check_file_notice'] = '注意：文件校验为根目录下所有文件以及phpcms、api、statics三个文件夹下所有目录和文件与默认程序同名文件md5值对比的结果，如果异常请用木马扫描工具扫描该文件是否包含木马';
$LANG['upgrade_notice'] = '注意：升级程序有可能覆盖模版文件，请注意备份！linux服务器需检查文件所有者权限和组权限，确保WEB SERVER用户有文件写入权限';