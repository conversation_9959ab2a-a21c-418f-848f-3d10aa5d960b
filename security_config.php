<?php
/**
 * PHPCMS 安全配置文件
 * 兼容 PHP 7.3-8.2
 * 
 * 使用方法：在 phpcms/base.php 开头包含此文件
 * require_once PHPCMS_PATH . 'security_config.php';
 */

// 防止直接访问
defined('IN_PHPCMS') or exit('No permission resources.');

// 1. 禁用危险函数
if (function_exists('ini_set')) {
    // 禁用危险函数
    $dangerous_functions = array(
        'eval', 'exec', 'system', 'passthru', 'shell_exec', 
        'popen', 'proc_open', 'file_get_contents', 'file_put_contents',
        'fopen', 'fwrite', 'fputs', 'fgets', 'fread',
        'assert', 'create_function', 'call_user_func', 'call_user_func_array'
    );
    
    $current_disabled = ini_get('disable_functions');
    $new_disabled = $current_disabled ? $current_disabled . ',' . implode(',', $dangerous_functions) : implode(',', $dangerous_functions);
    ini_set('disable_functions', $new_disabled);
    
    // 其他安全设置
    ini_set('expose_php', 'Off');
    ini_set('display_errors', 'Off');
    ini_set('log_errors', 'On');
    ini_set('allow_url_fopen', 'Off');
    ini_set('allow_url_include', 'Off');
    ini_set('register_globals', 'Off');
    ini_set('magic_quotes_gpc', 'Off');
    ini_set('session.cookie_httponly', '1');
    ini_set('session.cookie_secure', '1');
    ini_set('session.use_strict_mode', '1');
    
    // 设置开放目录限制
    $open_basedir = dirname(__FILE__) . PATH_SEPARATOR . sys_get_temp_dir();
    ini_set('open_basedir', $open_basedir);
}

// 2. 输入过滤增强
function security_filter_input($data) {
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            $data[$key] = security_filter_input($value);
        }
        return $data;
    }
    
    if (!is_string($data)) {
        return $data;
    }
    
    // 移除危险字符
    $data = str_replace(array("\0", "\r"), '', $data);
    
    // 过滤危险函数调用
    $dangerous_patterns = array(
        '/eval\s*\(/i',
        '/exec\s*\(/i', 
        '/system\s*\(/i',
        '/passthru\s*\(/i',
        '/shell_exec\s*\(/i',
        '/assert\s*\(/i',
        '/create_function\s*\(/i',
        '/<\?php/i',
        '/<\?=/i',
        '/<script/i'
    );
    
    foreach ($dangerous_patterns as $pattern) {
        $data = preg_replace($pattern, '', $data);
    }
    
    return $data;
}

// 3. 应用输入过滤
if (!empty($_GET)) {
    $_GET = security_filter_input($_GET);
}
if (!empty($_POST)) {
    $_POST = security_filter_input($_POST);
}
if (!empty($_COOKIE)) {
    $_COOKIE = security_filter_input($_COOKIE);
}
if (!empty($_REQUEST)) {
    $_REQUEST = security_filter_input($_REQUEST);
}

// 4. 文件上传安全检查
function security_check_upload($file_path) {
    if (!file_exists($file_path)) {
        return false;
    }
    
    // 检查文件内容
    $content = file_get_contents($file_path, false, null, 0, 1024);
    
    // 检查是否包含PHP代码
    if (preg_match('/<\?php|<\?=|<script.*php/i', $content)) {
        unlink($file_path); // 删除危险文件
        return false;
    }
    
    return true;
}

// 5. SQL注入防护增强
function security_escape_sql($string) {
    if (is_array($string)) {
        foreach ($string as $key => $value) {
            $string[$key] = security_escape_sql($value);
        }
        return $string;
    }
    
    // 移除SQL注入关键词
    $sql_keywords = array(
        'union', 'select', 'insert', 'update', 'delete', 'drop', 
        'create', 'alter', 'exec', 'execute', 'script', 'declare'
    );
    
    foreach ($sql_keywords as $keyword) {
        $string = preg_replace('/\b' . $keyword . '\b/i', '', $string);
    }
    
    return addslashes($string);
}

// 6. 设置错误处理
function security_error_handler($errno, $errstr, $errfile, $errline) {
    // 记录错误但不显示
    $error_msg = date('Y-m-d H:i:s') . " Error: [$errno] $errstr in $errfile on line $errline\n";
    error_log($error_msg, 3, CACHE_PATH . 'security_errors.log');
    
    // 对于严重错误，返回通用错误页面
    if ($errno == E_ERROR || $errno == E_PARSE || $errno == E_CORE_ERROR) {
        http_response_code(500);
        exit('System temporarily unavailable');
    }
    
    return true;
}

// 设置错误处理器
set_error_handler('security_error_handler');

// 7. 会话安全
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 防止会话固定攻击
if (!isset($_SESSION['initiated'])) {
    session_regenerate_id(true);
    $_SESSION['initiated'] = true;
}

// 8. CSRF保护
function security_generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function security_verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// 9. IP白名单检查（可选）
function security_check_ip_whitelist($allowed_ips = array()) {
    if (empty($allowed_ips)) {
        return true; // 如果没有设置白名单，允许所有IP
    }
    
    $client_ip = $_SERVER['REMOTE_ADDR'];
    return in_array($client_ip, $allowed_ips);
}

// 10. 请求频率限制
function security_rate_limit($max_requests = 100, $time_window = 3600) {
    $client_ip = $_SERVER['REMOTE_ADDR'];
    $cache_key = 'rate_limit_' . md5($client_ip);
    
    // 这里需要配合缓存系统使用，简化版本
    if (!isset($_SESSION[$cache_key])) {
        $_SESSION[$cache_key] = array('count' => 0, 'start_time' => time());
    }
    
    $rate_data = $_SESSION[$cache_key];
    
    if (time() - $rate_data['start_time'] > $time_window) {
        // 重置计数器
        $_SESSION[$cache_key] = array('count' => 1, 'start_time' => time());
        return true;
    }
    
    if ($rate_data['count'] >= $max_requests) {
        http_response_code(429);
        exit('Too Many Requests');
    }
    
    $_SESSION[$cache_key]['count']++;
    return true;
}

// 应用基本的频率限制
security_rate_limit();

?>
