# PHPCMS 简化安全配置 - 如果后台无法访问请使用此版本
# 将此文件重命名为 .htaccess 替换当前文件

# 禁止访问隐藏文件
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# 禁止访问备份和配置文件
<FilesMatch "\.(bak|backup|sql|log|conf)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# 禁止上传目录执行PHP
<Directory "uploadfile">
    <FilesMatch "\.(php|php3|php4|php5|phtml|pht)$">
        Order allow,deny
        Deny from all
    </FilesMatch>
</Directory>

# 禁止缓存目录中的PHP文件被直接访问
<Directory "caches">
    <FilesMatch "\.php$">
        Order allow,deny
        Deny from all
    </FilesMatch>
</Directory>

# 防止目录浏览
Options -Indexes

# 基本安全头部
<IfModule mod_headers.c>
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
    Header always set X-Content-Type-Options nosniff
</IfModule>

# 错误页面重定向
ErrorDocument 403 /index.php
ErrorDocument 404 /index.php
