{template "content","header"}
<link href="{CSS_PATH}table_form.css" rel="stylesheet" type="text/css" />
<link href="{CSS_PATH}dialog.css" rel="stylesheet" type="text/css" />

<div class="main">
	<div class="col-left">
    	<div class="crumbs"><a href="{siteurl($siteid)}">首页</a><span> &gt; </span><a href="{APP_PATH}index.php?m=formguide&c=index&a=index">表单向导</a> <span> &gt; </span> {$name}</div>
        <div id="Article">
        	<h1>{$name}</h1>
			<div class="content">
				<form method="post" action="?m=formguide&c=index&a=show&formid={$formid}" name="myform" id="myform">
<table class="table_form" width="100%" cellspacing="0">
<tbody>
 {loop $forminfos_data $field $info}
	{if $info['formtype']=='omnipotent'}
		{loop $forminfos_data $_fm $_fm_value}
			{if $_fm_value['iscomnipotent']}
				{php $info['form'] = str_replace('{'.$_fm.'}',$_fm_value['form'],$info['form']);}
			{/if}
		{/loop}
	{/if}
	<tr>
      <th width="80">{if $info['star']} <font color="red">*</font>{/if} {$info['name']}
	  </th>
      <td>{$info['form']}  {$info['tips']}</td>
    </tr>
{/loop}
	</tbody>
</table>
<input type="submit" name="dosubmit" id="dosubmit" value=" 提交 ">&nbsp;<input type="reset" value=" 取消 ">
</form>
			</div>
      </div>
      <div class="bk10"></div>
  </div>
    <div class="col-auto">
        <div class="box">
            <h5 class="title-2">频道总排行</h5>
            <ul class="content digg">
			{pc:content  action="hits" catid="$catid" num="10" order="views DESC" cache="3600"}
				{loop $data $r}
					<li><a href="{$r[url]}" target="_blank" title="{$r[title]}">{str_cut($r[title], 28, '')}</a></li>
				{/loop}
			{/pc}
            </ul>
        </div>
        <div class="bk10"></div>
        <div class="box">
            <h5 class="title-2">频道本月排行</h5>
            <ul class="content rank">
			{pc:content action="hits" catid="$catid" num="10" order="monthviews DESC" cache="3600"}
				{loop $data $r}
				<li><span>{number_format($r[monthviews])}</span><a href="{$r[url]}"{title_style($r[style])} class="title" title="{$r[title]}">{str_cut($r[title],56,'...')}</a></li>
				{/loop}
			{/pc}
            </ul>
        </div>
    </div>
</div>
<script type="text/javascript">
<!--
	function show_ajax(obj) {
		var keywords = $(obj).text();
		var offset = $(obj).offset();
		var jsonitem = '';
		$.getJSON("{APP_PATH}index.php?m=content&c=index&a=json_list&type=keyword&modelid={$modelid}&id={$id}&keywords="+encodeURIComponent(keywords),
				function(data){
				var j = 1;
				var string = "<div class='point key-float'><div style='position:relative'><div class='arro'></div>";
				string += "<a href='JavaScript:;' onclick='$(this).parent().parent().remove();' hidefocus='true' class='close'><span>关闭</span></a><div class='contents f12'>";
				if(data!=0) {
				  $.each(data, function(i,item){
					j = i+1;
					jsonitem += "<a href='"+item.url+"' target='_blank'>"+j+"、"+item.title+"</a><BR>";
					
				  });
					string += jsonitem;
				} else {
					string += '没有找到相关的信息！';
				}
					string += "</div><span class='o1'></span><span class='o2'></span><span class='o3'></span><span class='o4'></span></div></div>";		
					$(obj).after(string);
					$('.key-float').mouseover(
						function (){
							$(this).siblings().css({"z-index":0})
							$(this).css({"z-index":1001});
						}
					)
					$(obj).next().css({ "left": +offset.left-100, "top": +offset.top+$(obj).height()+12});
				});
	}

	function add_favorite(title) {
		$.getJSON('{APP_PATH}api.php?op=add_favorite&title='+title+'&url='+encodeURIComponent(location.href)+'&'+Math.random()+'&callback=?', function(data){
			if(data.status==1)	{
				$("#favorite").html('收藏成功');
			} else {
				alert('请登录');
			}
		});
	}

$(function(){
  $('#Article .content img').LoadImage(true, 660, 660,'{IMG_PATH}s_nopic.gif');    
})
//-->
</script>

<script language="JavaScript" src="{APP_PATH}api.php?op=count&id={$id}&modelid={$modelid}"></script>
{template "content","footer"}