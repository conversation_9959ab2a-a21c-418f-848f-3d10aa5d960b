<?php
$LANG['operation'] = 'Operation';
$LANG['view'] = 'View';
$LANG['access'] = 'Visit';

$LANG['view_code'] = 'View code';
$LANG['modifyfile'] = 'Modified';
$LANG['lastmodifytime'] = 'Last modified time';
$LANG['modifyedfile'] = 'Modified file';
$LANG['lostfile'] = 'Missing file';
$LANG['unknowfile'] = 'Unknown file';
$LANG['filesize'] = 'File size';
$LANG['begin_checkfile'] = 'Verifying, please wait...';
$LANG['begin_upgrade'] = 'Upgrading';
$LANG['upgradeing'] = 'Upgrading';
$LANG['upgrade_success'] = 'Upgraded successfully!';
$LANG['lost'] = 'Lost';
$LANG['file_address'] = 'File location';
$LANG['please_check_filepri'] = 'Failed to copy file, please check path/permissions';
$LANG['check_file_notice'] = 'Note: File verification is a comparable result between all files of the root directory, all files and subdirectories under phpcms, api, statics folder and MD5 value of same name in default program. If an exception occurs, please scan your files to detect the Trojan file';
$LANG['upgrade_notice'] = 'Note: Upgrade program may overwrite template files, please backup your files before upgrade! It is essential to check the owner and group of file permissions in the Linux server. Make sure WEB SERVER user sets write permissions to files';