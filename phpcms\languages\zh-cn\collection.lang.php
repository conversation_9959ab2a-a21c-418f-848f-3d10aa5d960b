<?php
$LANG['node_add'] = '添加采集点';
$LANG['nodename'] = '名称';
$LANG['modelname'] = '模型名称';
$LANG['charset'] = '编码';
$LANG['content_num'] = '文章数';
$LANG['operation'] = '操作';
$LANG['adddate'] = '添加时间';
$LANG['lastdate'] = '最后采集时间';
$LANG['sure_delete'] = '您确定要删除吗？';
$LANG['basic_configuration'] = '基本信息';
$LANG['more_configuration'] = '详细信息';
$LANG['urlslist'] = '网址列表';
$LANG['sequence'] = '序列网址';
$LANG['multiple_pages'] = '多个网页';
$LANG['single_page'] = '单一网页';
$LANG['select_model'] = '请选择模型';
$LANG['not_to_collect'] = '没有内容可采集';
$LANG['url_collect_msg'] = '没有找到网址列表，请先进行网址采集。';
$LANG['collectioning'] = '采集正在进行中，采集进度:';
$LANG['collection_success'] = '采集完成！';
$LANG['please_select_cat'] = '请选择栏目';
$LANG['illegal_section_parameter'] = '非法栏目参数';
$LANG['model_does_not_exist_please_update_the_cache_model'] = '模型不存在！请更新模型缓存。';
$LANG['please_choose'] = '请选择';
$LANG['author'] = '作者';
$LANG['comeform'] = '来源';
$LANG['time'] = '时间';
$LANG['content'] = '内容';
$LANG['node_not_found'] = '采集节点没有找到';
$LANG['article_was_imported'] = '篇文章被导入。';
$LANG['are_imported_the_import_process'] = '正在导入中，导入进度：';
$LANG['url_list'] = '网址列表';
$LANG['in_all'] = '总共';
$LANG['all_count_msg'] = '条记录。重复记录';
$LANG['import_num_msg'] = '。去除重复记录，共入库';
$LANG['following_operation'] = '您现在可以还可以做以下的操作';
$LANG['following_operation_1'] = '1、开始采集文章内容';
$LANG['following_operation_2'] = '2、返回采集点管理';
$LANG['the_new_publication_solutions'] = '新建发布方案';
$LANG['category'] = '栏目';
$LANG['the_withdrawal_of_the_summary'] = '自动提取摘要';
$LANG['if_the_contents_of_intercepted'] = '是否截取内容';
$LANG['characters_to_a_summary_of_contents'] = '字符至内容摘要';
$LANG['the_withdrawal_of_thumbnails'] = '自动提取缩略图';
$LANG['whether_access_to_the_content_of'] = '是否获取内容第';
$LANG['picture_a_caption_pictures'] = '张图片作为标题图片';
$LANG['import_article_state'] = '导入文章状态';
$LANG['pendingtrial'] = '待审';
$LANG['fantaoboys'] = '审核通过';
$LANG['corresponding_labels_and_a_database_ties'] = '标签与数据库对应关系';
$LANG['the_original_database_field'] = '原数据库字段';
$LANG['explain'] = '数据库字段说明';
$LANG['label_field__collected_with_the_result_'] = '标签字段（采集填充结果）';
$LANG['publish_the_list'] = '发布方案列表';
$LANG['had_collected_from_the_roll'] = '原采集点名';
$LANG['the_new_gathering'] = '新采集点名';
$LANG['url_rewrites'] = '网址规则';
$LANG['content_rules'] = '内容规则';
$LANG['custom_rule'] = '自定义规则';
$LANG['eigrp'] = '高级配置';
$LANG['collection_items_of'] = '采集项目名';
$LANG['encode_varchar'] = '采集页面编码';
$LANG['web_sites_to_collect'] = '网址采集';
$LANG['url_type'] = '网址类型';
$LANG['url_configuration'] = '网址配置';
$LANG['test'] = '测试';
$LANG['url_msg'] = ' (如：http://www.phpcms.cn/help/rumen/(*).html,页码使用<a href="javascript:insertText(\'urlpage_1\', \'(*)\')">(*)</a>做为通配符。';
$LANG['page_from'] = '页码从';
$LANG['from'] = '从';
$LANG['to'] = '到';
$LANG['finish'] = '结束';
$LANG['increment_by'] = '每次增加 ';
$LANG['one_per_line'] = '每行一条';
$LANG['site_must_contain'] = '网址中必须包含 ';
$LANG['the_web_site_does_not_contain'] = '网址中不得包含  ';
$LANG['base_configuration'] = 'Base配置';
$LANG['base_msg'] = '如果目标网站配置了Base请设置。';
$LANG['get_url'] = '获取网址';
$LANG['rule_msg'] = '1、匹配规则请设置开始和结束符，具体内容使用“[内容]”做为通配符 。<BR>2、过滤选项格式为“要过滤的内容[|]替换值”，要过滤的内容支持正则表达式，每行一条。<BR>';
$LANG['expand_all'] = '全部展开';
$LANG['all_the'] = '全部合上';
$LANG['rule'] = '规则';
$LANG['matching_rule'] = '匹配规则';
$LANG['[content]'] = '[内容]';
$LANG['use'] = '使用';
$LANG['w_wildmenu'] = '作为通配符';
$LANG['filtering'] = '过滤选项';
$LANG['select'] = '选择';
$LANG['content_page'] = '内容分页';
$LANG['page_mode'] = '分页模式';
$LANG['all_are_models'] = '全部列出模式';
$LANG['down_the_pages_mode'] = '上下页模式';
$LANG['nextpage_rule'] = '下一页规则';
$LANG['nextpage_rule_msg'] = '请填写下一页超链接中间的代码。如：<a href="http://www.phpcms.cn/page_1.html">下一页</a>，他的“下一页规则”为“下一页”。';
$LANG['add_item'] = '添加项目';
$LANG['rulename'] = '规则名';
$LANG['rules_in_english'] = '规则英文名';
$LANG['download_pic'] = '下载图片';
$LANG['no_download'] = '不下载';
$LANG['watermark'] = '图片水印';
$LANG['gfl_sdk'] = '打水印';
$LANG['no_gfl_sdk'] = '不打水印';
$LANG['content_page_models'] = '内容分页';
$LANG['no_page'] = '不分页';
$LANG['by_the_paging'] = '按原文分页';
$LANG['sort_order'] = '导入顺序';
$LANG['with_goals_from_the_same'] = '与目标站相同';
$LANG['and_objectives_of_the_standing_opposite'] = '与目标站相反';
$LANG['testpageurl'] = '测试地址';
$LANG['invert'] = '反选';
$LANG['select_all'] = '全选';
$LANG['collect_call'] = '采集点名';
$LANG['cfg'] = '配置文件';
$LANG['only_support_txt_file_upload'] = '只支持.txt文件上传';
$LANG['collection_web_site'] = '采集网址';
$LANG['collection_content'] = '采集内容';
$LANG['public_content'] = '内容发布';
$LANG['copy'] = '复制';
$LANG['export'] = '导出';
$LANG['import_collection_points'] = '导入采集点';
$LANG['data_acquisition_testdat'] = '测试采集';
$LANG['copy_node'] = '复制采集';
$LANG['import_collection_points'] = '导入采集点';
$LANG['view'] = '查看';
$LANG['loading'] = '加载中...';
$LANG['content_view'] = '内容查看';
$LANG['content_list'] = '文章列表';
$LANG['all'] = '全部';
$LANG['if_bsnap_then'] = '未采集';
$LANG['spidered'] = '已采集';
$LANG['imported'] = '已导入';
$LANG['status'] = '状态';
$LANG['url'] = '网址';
$LANG['also_delete_the_historical'] = '同时删除历史';
$LANG['import_selected'] = '导入选中';
$LANG['import_all'] = '全部导入';
$LANG['select_article'] = '请选择文章';
$LANG['program_add_operation_success'] = '添加发布方案成功!';
$LANG['handler_functions'] = '处理函数';
?>