<?php 

$LANG['enter_the_cache_input_will_not_be_cached']	=	'Please input cache time (NOT cached by Default)';
$LANG['cache_time_can_only_be_positive']			=	'The value only can be positive number.';
$LANG['that_shows_only_positive_numbers']			=	'The number of records only can be positive.';
$LANG['num']										=	'Number of calls';
$LANG['name']										=	'Name';
$LANG['should']										=	'should';
$LANG['is_greater_than']							=	'greater than';
$LANG['less_than']									=	'less than';
$LANG['lambda']										=	' ';
$LANG['sure_deleted']								=	'Are you sure you want to delete all of the selected options?';

$LANG['stdcall']									=	'Call mode';
$LANG['model_configuration']						=	'Model ';
$LANG['custom_sql']									=	'Custom SQL queries ';
$LANG['select_model']								=	'Select model';
$LANG['please_select_model']						=	'Please make a selection from the list';
$LANG['selectingoperation']							=	'Select operation';
$LANG['please_select_action']						=	'Please select a operation';
$LANG['please_enter_a_sql']							=	'Please input SQL query';
$LANG['vlan']										=	'Public settings';
$LANG['output_mode']								=	'Output mode';
$LANG['template']									=	'Template';
$LANG['buffer_time']								=	'Cache time';
$LANG['editing_data_sources_call']					=	'Configure DataSource invoking';
$LANG['valgrind']									=	'Your code';
$LANG['data_call']									=	'Calling code';
$LANG['copy_code']									=	'Your code has been saved to the clipboard';
$LANG['add_tag']									=	'Add tag wizard';
$LANG['create_tag_success']							=	'Tag wizard has been generated successfully.';
$LANG['block_name_not_empty']						=	'Block name is required';
$LANG['edit_tag_success']							=	'Tag wizard has been changed successfully.';
$LANG['stdcode']									=	'CODE';
$LANG['block']										=	'Block ';
$LANG['tag_call_setting']							=	'Tag configuration';
$LANG['over_dbsource']								=	'External data source';
$LANG['please_select_dbsource']						=	'Please select external data source';
$LANG['please_input_block_name']					=	'Please input block name. It should contain letters, digits ,underscores or variables)';
$LANG['ispage']										=	'Enable pagebreak';
$LANG['common_variables']							=	'Common variables';
$LANG['no_input_no_page']							=	'By default, pagebreak is disabled.';
$LANG['data_return']								=	'Return value';
$LANG['cache_times']								=	'Cache time';
$LANG['click_copy_code']							=	'Double-click CODE box to copy code to clipboard';
$LANG['return_value']								=	'Please input return value(The default value is data)';
?>