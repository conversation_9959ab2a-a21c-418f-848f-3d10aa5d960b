# PHPCMS 安全加固配置
# 兼容 PHP 7.3-8.2

# 禁止访问敏感文件和目录
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# 禁止访问配置文件
<FilesMatch "\.(inc|conf|config|sql|bak|backup|log)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# 禁止访问缓存目录中的PHP文件
<Directory "caches">
    <FilesMatch "\.php$">
        Order allow,deny
        Deny from all
    </FilesMatch>
</Directory>

# 禁止上传目录执行PHP
<Directory "uploadfile">
    <FilesMatch "\.(php|php3|php4|php5|phtml|pht)$">
        Order allow,deny
        Deny from all
    </FilesMatch>
    # 只允许特定文件类型
    <FilesMatch "\.(jpg|jpeg|png|gif|pdf|doc|docx|xls|xlsx|txt|zip|rar)$">
        Order allow,deny
        Allow from all
    </FilesMatch>
</Directory>

# 防止目录浏览
Options -Indexes

# 隐藏服务器信息
ServerTokens Prod
ServerSignature Off

# 防止点击劫持
Header always append X-Frame-Options SAMEORIGIN

# XSS保护
Header always set X-XSS-Protection "1; mode=block"

# 内容类型嗅探保护
Header always set X-Content-Type-Options nosniff

# 严格传输安全(如果使用HTTPS)
# Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"

# 防止SQL注入和XSS的基本过滤
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # 阻止常见的攻击模式
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} proc/self/environ [OR]
    RewriteCond %{QUERY_STRING} mosConfig_[a-zA-Z_]{1,21}(=|\%3D) [OR]
    RewriteCond %{QUERY_STRING} base64_(en|de)code[^(]*\([^)]*\) [OR]
    RewriteCond %{QUERY_STRING} (<|%3C)([^s]*s)+cript.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (<|%3C)([^e]*e)+mbed.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (<|%3C)([^o]*o)+bject.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (eval\(|eval%28) [NC,OR]
    RewriteCond %{QUERY_STRING} (exec\(|exec%28) [NC,OR]
    RewriteCond %{QUERY_STRING} (system\(|system%28) [NC,OR]
    RewriteCond %{QUERY_STRING} (passthru\(|passthru%28) [NC,OR]
    RewriteCond %{QUERY_STRING} (shell_exec\(|shell_exec%28) [NC]
    RewriteRule ^(.*)$ - [F,L]
    
    # 阻止文件包含攻击
    RewriteCond %{QUERY_STRING} \.\./\.\./\.\./
    RewriteRule ^(.*)$ - [F,L]
    
    # 阻止PHP代码注入
    RewriteCond %{QUERY_STRING} (\<\?php|\<\?=) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# 限制请求大小
LimitRequestBody 10485760

# 设置安全的Cookie
<IfModule mod_headers.c>
    Header edit Set-Cookie ^(.*)$ $1;HttpOnly;Secure;SameSite=Strict
</IfModule>

# 错误页面
ErrorDocument 403 /index.php
ErrorDocument 404 /index.php
ErrorDocument 500 /index.php
