{template "content","header"}
<!--main-->
<div class="main">
	<div class="col-left">
    	<div class="crumbs"><a href="{siteurl($siteid)}">首页</a><span> > </span>表单向导 列表</div>

        <ul class="list lh24 f14">
{loop $datas $r}
	<li><span class="rt">{date('Y-m-d H:i:s',$r[addtime])}</span>·<a href="{APP_PATH}index.php?m=formguide&c=index&a=show&formid={$r['modelid']}" target="_blank">{$r[name]}</a></li>
	{if $n%5==0}<li class="bk20 hr"></li>{/if}
{/loop}
        </ul>
        <div id="pages" class="text-c">{$pages}</div>
  </div>
    <div class="col-auto">
        <div class="box">
            <h5 class="title-2">频道总排行</h5>
             {pc:content action="hits" catid="$catid" num="10" order="views DESC"}
            <ul class="content digg">
				{loop $data $r}
					<li><a href="{$r[url]}" target="_blank">{$r[title]}</a></li>
				{/loop}
            </ul>
            {/pc}
        </div>
        <div class="bk10"></div>
        <div class="box">
            <h5 class="title-2">频道本月排行</h5>
             {pc:content action="hits" catid="$catid" num="8" order="monthviews DESC"}
            <ul class="content rank">
				{loop $data $r}
				<li><span>{number_format($r[monthviews])}</span><a href="{$r[url]}"{title_style($r[style])} class="title" title="{$r[title]}">{str_cut($r[title],56,'...')}</a></li>
				{/loop}
            </ul>
            {/pc}
        </div>
    </div>
</div>
{template "content","footer"}