<?php

/*Language Format:
Add a new file(.lang.php) with your module name at /phpcms/languages/
translation save at the array:$LANG
*/
$LANG['poster']							=	'Advertising';
$LANG['setting_updates_successful']		=	'Updated successfully';
$LANG['name_plates_not_empty']			=	'Position name is required';
$LANG['plate_width_not_empty']			=	'Width is required';
$LANG['plate_height_not_empty']			=	'Height is required';
$LANG['added_successful']				=	'Added successfully';
$LANG['edited_successful']				=	'Edited successfully';
$LANG['boardtype']						=	'Position name';
$LANG['size_format']					=	'Size';
$LANG['plate_width']					=	'Width';
$LANG['plate_height']					=	'Height';
$LANG['ad_list']						=	'Advertising list';
$LANG['random_one']						=	'Get one random value';
$LANG['random_all']						=	'List all';
$LANG['description']					=	'Description';
$LANG['js_path']						=	'JS path';
$LANG['ads_num']						=	'Num of ads';
$LANG['banner']							=	'Rectangular banner';
$LANG['fixure']							=	'Fixed position';
$LANG['float']							=	'Floating ads';
$LANG['couplet']						=	'Couplet ads';
$LANG['imagechange']					=	'Image rotation ads';
$LANG['imagelist']						=	'Image ads';
$LANG['text']							=	'Text ads';
$LANG['code']							=	'Calling code';
$LANG['position']						=	'Advertising position';
$LANG['left_margin']					=	'Left margin';
$LANG['top_margin']						=	'Top margin';
$LANG['lightbox']						=	'Full-screen centered';
$LANG['rolling']						=	'The ads will move on the screen as you drag the mouse';
$LANG['ads_type']						=	'Type';
$LANG['remove_all_selected']			=	'Remove all selected';
$LANG['poster_title']					=	'Ads title';
$LANG['for_postion']					=	'Position';
$LANG['poster_type']					=	'Type';
$LANG['line_time']						=	'Login time';
$LANG['down_time']						=	'LogoutTime';
$LANG['photo_setting']					=	'Photo settings';
$LANG['linkurl']						=	'URL';
$LANG['flash_url']						=	'Flash url';
$LANG['upload_photo']					=	'Upload photo';
$LANG['flash_setting']					=	'Flash settings';
$LANG['alt']							=	'Text prompt';
$LANG['flash_upload']					=	'Upload flash';	
$LANG['word_link']						=	'Text link';
$LANG['code_setting']					=	'Code settings';
$LANG['word_content']					=	'Textual content ';
$LANG['code_content']					=	'Code';
$LANG['add_space']						=	'Add advertising position';
$LANG['flash']							=	'Flash';
$LANG['photo']							=	'Image';
$LANG['title']							=	'Text';
$LANG['add_ads']						=	'Add ads';
$LANG['add_ads_success']				=	'Added successfully';
$LANG['no_create_js']					=	'JS cannot be generated without position ID';
$LANG['space_exist']					=	'The position already exists';
$LANG['adsname_no_empty']				=	'Title of advertising is required';
$LANG['no_ads_type']					=	'Type of advertising is required';
$LANG['no_setting_photo']				=	'Not set image';
$LANG['no_flash_path']					=	'Not set flash path';
$LANG['no_title_info']					=	'Not set text info';
$LANG['please_input_name']				=	'Please input ads title';
$LANG['name_three_length']				=	'At least 6 characters';
$LANG['correct']						=	'Correct';
$LANG['server_busy']					=	'Unable to return any data. This may be caused by the server being busy. Please try again';
$LANG['ads_exist']						=	'The ads already exists';
$LANG['checking']						=	'Verifying the legitimacy...';
$LANG['choose_ads_type']				=	'Please select ads type';
$LANG['type_selected']					=	'Must select a type';
$LANG['online_time']					=	'Please select ads publishing time';
$LANG['one_month_no_select']			=	'The default value is one month';
$LANG['down_time']						=	'Please select ads withdrawal time';
$LANG['link_content']					=	'Input content of text link';
$LANG['no_link_content']				=	'No input';
$LANG['input_code']						=	'Enter code';
$LANG['space_list']						=	'Section list';
$LANG['status']							=	'Status';
$LANG['hits']							=	'Hits';
$LANG['addtime']						=	'Added time';
$LANG['stop']							=	'Suspend';
$LANG['past']							=	'Expired';
$LANG['start']							=	'Enable';
$LANG['stat']							=	'Statistics';
$LANG['edit_ads']						=	'Edit ads';
$LANG['ads_module']						=	'Advertising module';

//广告统计
$LANG['hits_stat']						=	'Click to calculate';
$LANG['show_stat']						=	'Show result';
$LANG['listorder_f_area']				=	'By area';
$LANG['listorder_f_ip']					=	'By IP';
$LANG['all']							=	'All';
$LANG['today']							=	'Today';
$LANG['yesterday']						=	'Yesterday';
$LANG['one_week']						=	'A week';
$LANG['two_week']						=	'Two weeks';
$LANG['one_month']						=	'A month';
$LANG['history_select']					=	'View history';
$LANG['browse_ip']						=	'View IP';
$LANG['for_area']						=	'Area';
$LANG['show_times']						=	'Impressions';
$LANG['come_from']						=	'Source';
$LANG['visit_time']						=	'Visited time';
$LANG['operate']						=	'Operation';
$LANG['click']							=	'Click';
$LANG['show']							=	'Show';

//setting
$LANG['ads_setting']					=	'Ads module settings';
$LANG['ads_show_time']					=	'Calculate the number of ads display';
$LANG['upload_file_ext']				=	'Upload file type';
$LANG['file_size']						=	'File size';

//space_add\space_edit
$LANG['please_input_space_name']		=	'Please input version name';
$LANG['spacename_three_length']			=	'At least 6 characters';
$LANG['spacename_illegality']			=	'Your position name is invalid, please check it again.';
$LANG['space_exist']				 	=	'The position already exists. Please change the position name';
$LANG['choose_space_type']				=	'Select position type';
$LANG['input_width_height']				=	'Enter width and height';
$LANG['three_numeric']					=	'No more than 3 digits';
$LANG['get_code_space']					=	'Call ads section';

//调用代码
$LANG['explain']						=	'Call descriptions';
$LANG['notice']							=	'1. Method 1, no server environment is required. It is able to calculate the number of ads displays and also automatically justify whether ads is expired or not. However, method 1 consumes too much server resources and the user visits a website with slow rate. In addition, it does not support JS advertisement codes such as Google JS codes<br />2. Method 2, no server environment is required. It allows to visit ads fast , while almost no consumption of server resources to deal witha lot of good customers here but it is unable to automatically justify whether ads is expired or not which is required to update JS frequently. In addition, it does not support JS advertisement codes such as Google JS codes.<br />3. If you want to call advertisement codes such as google, baidu It only can have one way. Please update advertising in the static pages after altering ads codes when website uses static page.<br />4. Please select one calling method according to own situations and then copy codes to template that we want to display. After then, it is necessary to update related sites.';
$LANG['one_way']						=	'Method 1';
$LANG['js_code']						=	'JS calling code (call PHP dynamic code)';
$LANG['this_way_stat_show']				=	'This method is able to calculate the number of displays';
$LANG['copy_code']						=	'Copy to clipboard ';
$LANG['second_code']					=	'Method 2';
$LANG['js_code_html']					=	'JS calling code (call JS static code)';

//space_list
$LANG['preview']						=	'Preview';
$LANG['get_code']						=	'Calling code';
$LANG['edit_space']						=	'Edit position';
$LANG['module_setting']					=	'Module settings';
$LANG['update_js_success']				=	'Update JS successfully';
$LANG['update_js']						=	'Update JS successfully';

//模板设置
$LANG['template_name']					=	'Template name';
$LANG['setting_success']				=	'Configuration successful';
$LANG['check_template']					=	'View';
$LANG['setting_template']				=	'Settings';
$LANG['template_file_name']				=	'Template file name';
$LANG['name_cn']						=	'Chinese name';
$LANG['show_this_param']				=	'Enable the following properties';
$LANG['this_param_selected']			=	'Properties as above will be chosen?';
$LANG['is_set_space']					=	'Set section position';
$LANG['is_set_size']					=	'Set section size';
$LANG['space_poster']					=	'Advertising under section';
$LANG['all_list']						=	'List all';
$LANG['only_one']						=	'List one based on ordering';
$LANG['is_used_type']					=	'Available ads type';
$LANG['max_add_param']					=	'Max number of added pictures';
?>