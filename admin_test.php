<?php
/**
 * PHPCMS 后台访问测试文件
 * 用于检测后台是否可以正常访问
 * 测试完成后请删除此文件
 */

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='utf-8'><title>后台访问测试</title>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";
echo "</head><body>";

echo "<h1>🔧 PHPCMS 后台访问测试</h1>";
echo "<p class='info'>测试时间: " . date('Y-m-d H:i:s') . "</p>";

// 测试1: 检查基本文件
echo "<h2>1. 基本文件检查</h2>";
$files_to_check = array(
    'index.php' => '主入口文件',
    'admin.php' => '后台入口文件', 
    'phpcms/base.php' => '框架基础文件',
    'phpcms/modules/admin/index.php' => '后台模块文件'
);

foreach ($files_to_check as $file => $desc) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ {$desc}: {$file}</p>";
    } else {
        echo "<p class='error'>❌ {$desc}: {$file} - 文件不存在</p>";
    }
}

// 测试2: 检查.htaccess文件
echo "<h2>2. .htaccess 配置检查</h2>";
if (file_exists('.htaccess')) {
    echo "<p class='success'>✅ .htaccess 文件存在</p>";
    $htaccess_content = file_get_contents('.htaccess');
    if (strpos($htaccess_content, 'm=admin') !== false) {
        echo "<p class='success'>✅ .htaccess 包含后台访问白名单规则</p>";
    } else {
        echo "<p class='error'>❌ .htaccess 可能缺少后台访问规则</p>";
    }
} else {
    echo "<p class='info'>ℹ️ .htaccess 文件不存在 (可能不是问题)</p>";
}

// 测试3: 模拟后台访问
echo "<h2>3. 后台访问链接测试</h2>";
$admin_urls = array(
    'admin.php' => '后台重定向页面',
    'index.php?m=admin' => '后台主页面',
    'index.php?m=admin&c=index&a=init' => '后台初始化页面'
);

foreach ($admin_urls as $url => $desc) {
    echo "<p class='info'>🔗 {$desc}: <a href='{$url}' target='_blank'>{$url}</a></p>";
}

// 测试4: PHP环境检查
echo "<h2>4. PHP 环境检查</h2>";
echo "<p class='info'>PHP 版本: " . PHP_VERSION . "</p>";

$required_functions = array('mysqli_connect', 'json_decode', 'session_start', 'header');
foreach ($required_functions as $func) {
    if (function_exists($func)) {
        echo "<p class='success'>✅ 函数 {$func} 可用</p>";
    } else {
        echo "<p class='error'>❌ 函数 {$func} 不可用</p>";
    }
}

// 测试5: 权限检查
echo "<h2>5. 目录权限检查</h2>";
$dirs_to_check = array('caches', 'uploadfile', 'phpcms');
foreach ($dirs_to_check as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<p class='success'>✅ 目录 {$dir} 可写</p>";
        } else {
            echo "<p class='error'>❌ 目录 {$dir} 不可写</p>";
        }
    } else {
        echo "<p class='error'>❌ 目录 {$dir} 不存在</p>";
    }
}

// 解决方案建议
echo "<h2>6. 问题解决方案</h2>";
echo "<div style='background:#f0f0f0;padding:15px;border-radius:5px;'>";
echo "<h3>如果后台仍无法访问，请尝试以下方法：</h3>";
echo "<ol>";
echo "<li><strong>方法1 - 使用简化配置:</strong><br>";
echo "将 <code>.htaccess.backup</code> 重命名为 <code>.htaccess</code> 替换当前文件</li>";
echo "<li><strong>方法2 - 临时禁用.htaccess:</strong><br>";
echo "将 <code>.htaccess</code> 重命名为 <code>.htaccess.disabled</code></li>";
echo "<li><strong>方法3 - 检查Apache配置:</strong><br>";
echo "确保Apache启用了mod_rewrite模块</li>";
echo "<li><strong>方法4 - 直接访问:</strong><br>";
echo "尝试直接访问: <a href='index.php?m=admin&c=index&a=login'>index.php?m=admin&c=index&a=login</a></li>";
echo "</ol>";
echo "</div>";

// 快速修复按钮
echo "<h2>7. 快速修复</h2>";
echo "<div style='margin:20px 0;'>";
echo "<button onclick='useSimpleHtaccess()' style='background:#28a745;color:white;padding:10px 20px;border:none;border-radius:5px;cursor:pointer;margin:5px;'>使用简化.htaccess</button>";
echo "<button onclick='disableHtaccess()' style='background:#ffc107;color:black;padding:10px 20px;border:none;border-radius:5px;cursor:pointer;margin:5px;'>禁用.htaccess</button>";
echo "<button onclick='deleteTestFile()' style='background:#dc3545;color:white;padding:10px 20px;border:none;border-radius:5px;cursor:pointer;margin:5px;'>删除测试文件</button>";
echo "</div>";

echo "<script>";
echo "function useSimpleHtaccess() {";
echo "  if(confirm('将使用简化的.htaccess配置，这会降低一些安全性但确保后台可访问。确定吗？')) {";
echo "    fetch('admin_test.php?action=simple_htaccess').then(r=>r.text()).then(d=>{alert(d);location.reload();});";
echo "  }";
echo "}";
echo "function disableHtaccess() {";
echo "  if(confirm('将临时禁用.htaccess文件。确定吗？')) {";
echo "    fetch('admin_test.php?action=disable_htaccess').then(r=>r.text()).then(d=>{alert(d);location.reload();});";
echo "  }";
echo "}";
echo "function deleteTestFile() {";
echo "  if(confirm('删除此测试文件？')) {";
echo "    fetch('admin_test.php?action=delete').then(r=>r.text()).then(d=>{alert(d);location.href='index.php';});";
echo "  }";
echo "}";
echo "</script>";

echo "</body></html>";

// 处理AJAX请求
if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'simple_htaccess':
            if (file_exists('.htaccess.backup')) {
                if (copy('.htaccess.backup', '.htaccess')) {
                    echo '简化.htaccess配置已应用';
                } else {
                    echo '应用失败，请手动操作';
                }
            } else {
                echo '.htaccess.backup文件不存在';
            }
            break;
            
        case 'disable_htaccess':
            if (file_exists('.htaccess')) {
                if (rename('.htaccess', '.htaccess.disabled')) {
                    echo '.htaccess已禁用';
                } else {
                    echo '禁用失败，请手动操作';
                }
            } else {
                echo '.htaccess文件不存在';
            }
            break;
            
        case 'delete':
            if (unlink(__FILE__)) {
                echo '测试文件已删除';
            } else {
                echo '删除失败，请手动删除';
            }
            break;
    }
    exit;
}
?>
